import { Router } from 'express';
import {
  signup,
  login,
  getProfile,
  updateProfile,
  changePassword,
  logout,
  registerUser
} from '../controllers/user.controller';
import { authMiddleware } from '../middlewares/auth.middleware';

const router = Router();

// Public routes (no authentication required)
router.post('/signup', signup);
router.post('/register', registerUser); // Alias for backward compatibility
router.post('/login', login);

// Protected routes (authentication required)
router.get('/profile', authMiddleware.verifyToken, getProfile);
router.put('/profile', authMiddleware.verifyToken, updateProfile);
router.post('/change-password', authMiddleware.verifyToken, changePassword);
router.post('/logout', authMiddleware.verifyToken, logout);

export default router;
