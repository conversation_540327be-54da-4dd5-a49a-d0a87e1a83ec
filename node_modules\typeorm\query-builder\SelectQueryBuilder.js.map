{"version": 3, "sources": ["../../src/query-builder/SelectQueryBuilder.ts"], "names": [], "mappings": ";;;AAAA,qGAAiG;AAEjG,8GAA0G;AAC1G,gGAA4F;AAC5F,oGAAgG;AAChG,8FAA0F;AAC1F,mDAA+C;AAC/C,2EAAuE;AACvE,oFAAgF;AAChF,qEAAiE;AACjE,yDAAsF;AACtF,qHAAiH;AACjH,8EAA0E;AAC1E,8HAA0H;AAC1H,iDAA6C;AAE7C,oGAAgG;AAYhG,sGAAkG;AAElG,qDAAiD;AACjD,uDAAmD;AACnD,sEAAkE;AAClE,oCAAuC;AAMvC,uEAAmE;AAEnE,+CAA2C;AAC3C,sFAAkF;AAElF,6DAAyD;AACzD,+DAA2D;AAC3D,2EAAuE;AAGvE;;GAEG;AACH,MAAa,kBACT,SAAQ,2BAAoB;IADhC;;QAIa,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAA;QAE/C,gBAAW,GAAoB,EAAE,CAAA;QACjC,YAAO,GAAa,EAAE,CAAA;QACtB,UAAK,GAOT,EAAE,CAAA;QACE,eAAU,GAAW,EAAE,CAAA;QACvB,aAAQ,GAIZ,EAAE,CAAA;QACE,sBAAiB,GAAuB,EAAE,CAAA;IA81IxD,CAAC;IA51IG,4EAA4E;IAC5E,6BAA6B;IAC7B,4EAA4E;IAE5E;;OAEG;IACH,QAAQ;QACJ,IAAI,GAAG,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;QAC9B,GAAG,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAA;QACjC,GAAG,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAA;QACpC,GAAG,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAClC,GAAG,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;QACnC,GAAG,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAA;QACrC,GAAG,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAA;QACpC,GAAG,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAA;QACrC,GAAG,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAA;QACzC,GAAG,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAClC,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE,CAAA;QAChB,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ;YAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA;QACtD,OAAO,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC,CAAA;IACzD,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E,cAAc,CAAC,WAAoC;QAC/C,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAC9B,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACvB,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,QAAQ;QACJ,MAAM,EAAE,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACpC,EAAE,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAA;QAChC,EAAE,CAAC,kBAAkB,GAAG,IAAI,CAAA;QAC5B,OAAO,EAAE,CAAA;IACb,CAAC;IA6BD;;;OAGG;IACH,MAAM,CACF,SAGgE,EAChE,kBAA2B;QAE3B,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,QAAQ,CAAA;QACvC,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;gBACvD,SAAS,EAAE,SAAS;aACvB,CAAC,CAAC,CAAA;QACP,CAAC;aAAM,IAAI,OAAO,SAAS,KAAK,UAAU,EAAE,CAAC;YACzC,MAAM,eAAe,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;YAClD,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC,CAAA;YACnD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC5B,SAAS,EAAE,eAAe,CAAC,QAAQ,EAAE;gBACrC,SAAS,EAAE,kBAAkB;aAChC,CAAC,CAAA;QACN,CAAC;aAAM,IAAI,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG;gBACzB,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAkB,EAAE;aAC1D,CAAA;QACL,CAAC;QAED,OAAO,IAAI,CAAA;IACf,CAAC;IAoBD;;OAEG;IACH,SAAS,CACL,SAGgE,EAChE,kBAA2B;QAE3B,IAAI,CAAC,SAAS;YAAE,OAAO,IAAI,CAAA;QAE3B,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAC1D,SAAS,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,CAAC,CAC3D,CAAA;QACL,CAAC;aAAM,IAAI,OAAO,SAAS,KAAK,UAAU,EAAE,CAAC;YACzC,MAAM,eAAe,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;YAClD,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC,CAAA;YACnD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC5B,SAAS,EAAE,eAAe,CAAC,QAAQ,EAAE;gBACrC,SAAS,EAAE,kBAAkB;aAChC,CAAC,CAAA;QACN,CAAC;aAAM,IAAI,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC5B,SAAS,EAAE,SAAS;gBACpB,SAAS,EAAE,kBAAkB;aAChC,CAAC,CAAA;QACN,CAAC;QAED,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;OAGG;IACH,gBAAgB,CAAC,YAAoB;QACjC,IAAI,CAAC,aAAa,CAAC,gBAAgB,GAAG,YAAY,CAAA;QAClD,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,WAAoB,IAAI;QAC7B,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,QAAQ,CAAA;QAC5C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,UAAoB;QAC3B,IAAI,CAAC,aAAa,CAAC,gBAAgB,GAAG,UAAU,CAAA;QAChD,OAAO,IAAI,CAAA;IACf,CAAC;IAED,SAAS;QACL,OAAO,IAAI,CAAC,IAAI,CACZ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc;YACjC,4BAA4B,EAChC,aAAa,CAChB,CAAA;IACL,CAAC;IAsBD;;;;OAIG;IACH,IAAI,CACA,YAEgE,EAChE,SAAiB;QAEjB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,SAAS,CAAC,CAAA;QAC/D,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;QAC1C,OAAO,IAAoC,CAAA;IAC/C,CAAC;IAoBD;;;OAGG;IACH,OAAO,CACH,YAEgE,EAChE,SAAiB;QAEjB,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,SAAS,CAAC,CAAA;QAC3D,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS;YAC7B,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;QAE1C,OAAO,IAAoC,CAAA;IAC/C,CAAC;IAqDD;;;;OAIG;IACH,SAAS,CACL,gBAGgE,EAChE,KAAa,EACb,SAAkB,EAClB,UAA0B;QAE1B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,gBAAgB,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,CAAA;QAClE,OAAO,IAAI,CAAA;IACf,CAAC;IAqDD;;;;OAIG;IACH,QAAQ,CACJ,gBAGgE,EAChE,KAAa,EACb,SAAkB,EAClB,UAA0B;QAE1B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,CAAA;QACjE,OAAO,IAAI,CAAA;IACf,CAAC;IAqDD;;;;OAIG;IACH,kBAAkB,CACd,gBAGgE,EAChE,KAAa,EACb,SAAkB,EAClB,UAA0B;QAE1B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;QACrB,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,CAAA;QAC9D,OAAO,IAAI,CAAA;IACf,CAAC;IAqDD;;;;OAIG;IACH,iBAAiB,CACb,gBAGgE,EAChE,KAAa,EACb,SAAkB,EAClB,UAA0B;QAE1B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;QACrB,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,CAAA;QAC7D,OAAO,IAAI,CAAA;IACf,CAAC;IAkED;;;;;;OAMG;IACH,mBAAmB,CACf,aAAqB,EACrB,gBAGgE,EAChE,KAAa,EACb,SAAkB,EAClB,UAA0B;QAE1B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;QACrB,IAAI,CAAC,IAAI,CACL,OAAO,EACP,gBAAgB,EAChB,KAAK,EACL,SAAS,EACT,UAAU,EACV,aAAa,EACb,IAAI,CACP,CAAA;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAmED;;;;;;OAMG;IACH,kBAAkB,CACd,aAAqB,EACrB,gBAGgE,EAChE,KAAa,EACb,SAAkB,EAClB,UAA0B,EAC1B,WAA+B;QAE/B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;QACrB,IAAI,CAAC,IAAI,CACL,OAAO,EACP,gBAAgB,EAChB,KAAK,EACL,SAAS,EACT,UAAU,EACV,aAAa,EACb,KAAK,EACL,WAAW,CACd,CAAA;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAkED;;;;;;OAMG;IACH,kBAAkB,CACd,aAAqB,EACrB,gBAGgE,EAChE,KAAa,EACb,SAAkB,EAClB,UAA0B;QAE1B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;QACrB,IAAI,CAAC,IAAI,CACL,MAAM,EACN,gBAAgB,EAChB,KAAK,EACL,SAAS,EACT,UAAU,EACV,aAAa,EACb,IAAI,CACP,CAAA;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAmED;;;;;;OAMG;IACH,iBAAiB,CACb,aAAqB,EACrB,gBAGgE,EAChE,KAAa,EACb,SAAkB,EAClB,UAA0B,EAC1B,WAA+B;QAE/B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;QACrB,IAAI,CAAC,IAAI,CACL,MAAM,EACN,gBAAgB,EAChB,KAAK,EACL,SAAS,EACT,UAAU,EACV,aAAa,EACb,KAAK,EACL,WAAW,CACd,CAAA;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAgDD;;;OAGG;IACH,oBAAoB,CAChB,aAAqB,EACrB,YAAoB,EACpB,kBAA2D,EAC3D,mBAE4B;QAE5B,MAAM,mBAAmB,GAAG,IAAI,yCAAmB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;QACvE,mBAAmB,CAAC,aAAa,GAAG,aAAa,CAAA;QACjD,mBAAmB,CAAC,YAAY,GAAG,YAAY,CAAA;QAC/C,IAAI,OAAO,kBAAkB,KAAK,QAAQ;YACtC,mBAAmB,CAAC,KAAK,GAAG,kBAAkB,CAAA;QAClD,IACI,OAAO,kBAAkB,KAAK,QAAQ;YACrC,kBAA0B,CAAC,eAAe;YAE3C,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAA;QAE9C,mBAAmB,CAAC,mBAAmB,GAAG,mBAAmB,CAAA;QAC7D,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;QAEjE,IAAI,mBAAmB,CAAC,QAAQ,CAAC,sBAAsB,EAAE,CAAC;YACtD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;gBAC3B,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,mBAAmB,CAAC,aAAa;gBACvC,QAAQ,EAAE,mBAAmB,CAAC,QAAQ,CAAC,sBAAsB;aAChE,CAAC,CAAA;QACN,CAAC;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;OAGG;IACH,uBAAuB,CACnB,aAAqB,EACrB,YAAoB,EACpB,SAAkB,EAClB,mBAE4B;QAE5B,MAAM,sBAAsB,GAAG,IAAI,+CAAsB,CACrD,IAAI,CAAC,aAAa,CACrB,CAAA;QACD,sBAAsB,CAAC,aAAa,GAAG,aAAa,CAAA;QACpD,sBAAsB,CAAC,YAAY,GAAG,YAAY,CAAA;QAClD,sBAAsB,CAAC,KAAK,GAAG,SAAS,CAAA;QACxC,sBAAsB,CAAC,mBAAmB,GAAG,mBAAmB,CAAA;QAChE,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAA;QAEvE,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;YAC3B,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,sBAAsB,CAAC,aAAa;SAC7C,CAAC,CAAA;QACF,IAAI,sBAAsB,CAAC,QAAQ,CAAC,sBAAsB,EAAE,CAAC;YACzD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;gBAC3B,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,sBAAsB,CAAC,aAAa;gBAC1C,QAAQ,EACJ,sBAAsB,CAAC,QAAQ,CAAC,sBAAsB;aAC7D,CAAC,CAAA;QACN,CAAC;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,kBAAkB,CAAC,OAGlB;QACG,2BAA2B;QAC3B,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAClE,IACI,OAAO,KAAK,SAAS;gBACrB,OAAO,CAAC,SAAS,KAAK,SAAS;gBAC/B,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBAEvD,OAAM;YAEV,IAAI,CAAC,oBAAoB,CACrB,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI;gBAC9B,GAAG;gBACH,QAAQ,CAAC,YAAY,EACzB,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI;gBAC9B,GAAG;gBACH,QAAQ,CAAC,YAAY,EACzB,OAAO,CACV,CAAA;QACL,CAAC,CAAC,CAAA;QACF,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;;OAKG;IACH,KAAK,CACD,KAKqB,EACrB,UAA0B;QAE1B,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,CAAA,CAAC,oFAAoF;QACnH,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC/C,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG;gBACxB,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE;aAC3C,CAAA;QACL,CAAC;QACD,IAAI,UAAU;YAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;OAGG;IACH,QAAQ,CACJ,KAKqB,EACrB,UAA0B;QAE1B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;YAC3B,IAAI,EAAE,KAAK;YACX,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;SAC3C,CAAC,CAAA;QACF,IAAI,UAAU;YAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;OAGG;IACH,OAAO,CACH,KAKqB,EACrB,UAA0B;QAE1B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;YAC3B,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;SAC3C,CAAC,CAAA;QACF,IAAI,UAAU;YAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,QAAiC;QACzC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAA;IAC3D,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,QAAiC;QAC5C,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAA;IAC9D,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,QAAiC;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAA;IAC7D,CAAC;IAED;;;;;;;OAOG;IACH,UAAU,CAAC,GAAgB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAA;IACvD,CAAC;IAED;;;;;;;OAOG;IACH,aAAa,CAAC,GAAgB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAA;IAC1D,CAAC;IAED;;;;;;;OAOG;IACH,YAAY,CAAC,GAAgB;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAA;IACzD,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,MAAc,EAAE,UAA0B;QAC7C,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAA;QACtE,IAAI,UAAU;YAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;OAGG;IACH,SAAS,CAAC,MAAc,EAAE,UAA0B;QAChD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAA;QACnE,IAAI,UAAU;YAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;OAGG;IACH,QAAQ,CAAC,MAAc,EAAE,UAA0B;QAC/C,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAA;QAClE,IAAI,UAAU;YAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAA;IACf,CAAC;IAgBD;;;;OAIG;IACH,OAAO,CAAC,OAAgB;QACpB,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC,CAAA;QAC3C,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,EAAE,CAAA;QACpC,CAAC;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,OAAe;QACtB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACzC,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,YAA+B;QAC3C,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YACxD,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;gBAC7B,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,2BAA2B,CAAA;YAC/D,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,YAAY,CAAA;YAChD,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAA;IACf,CAAC;IA6BD;;;;OAIG;IACH,OAAO,CACH,IAAgC,EAChC,QAAwB,KAAK,EAC7B,KAAoC;QAEpC,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,MAAM;YAC1D,MAAM,IAAI,oBAAY,CAClB,gFAAgF,CACnF,CAAA;QACL,IACI,KAAK,KAAK,SAAS;YACnB,KAAK,KAAK,aAAa;YACvB,KAAK,KAAK,YAAY;YAEtB,MAAM,IAAI,oBAAY,CAClB,8FAA8F,CACjG,CAAA;QAEL,IAAI,IAAI,EAAE,CAAC;YACP,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC3B,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAwB,CAAA;YAC1D,CAAC;iBAAM,CAAC;gBACJ,IAAI,KAAK,EAAE,CAAC;oBACR,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG;wBAC1B,CAAC,IAAc,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;qBACrC,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,EAAE,CAAC,IAAc,CAAC,EAAE,KAAK,EAAE,CAAA;gBAC7D,CAAC;YACL,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,EAAE,CAAA;QACpC,CAAC;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,UAAU,CACN,IAAY,EACZ,QAAwB,KAAK,EAC7B,KAAoC;QAEpC,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,MAAM;YAC1D,MAAM,IAAI,oBAAY,CAClB,gFAAgF,CACnF,CAAA;QACL,IACI,KAAK,KAAK,SAAS;YACnB,KAAK,KAAK,aAAa;YACvB,KAAK,KAAK,YAAY;YAEtB,MAAM,IAAI,oBAAY,CAClB,8FAA8F,CACjG,CAAA;QAEL,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAA;QACxD,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAA;QAC7C,CAAC;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,KAAc;QAChB,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;QACtD,IACI,IAAI,CAAC,aAAa,CAAC,KAAK,KAAK,SAAS;YACtC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;YAE/B,MAAM,IAAI,oBAAY,CAClB,yEAAyE,CAC5E,CAAA;QAEL,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,MAAe;QAClB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAA;QACxD,IACI,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK,SAAS;YACvC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YAEhC,MAAM,IAAI,oBAAY,CAClB,0EAA0E,CAC7E,CAAA;QAEL,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,IAAa;QACd,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;QACpD,IACI,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,SAAS;YACrC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YAE9B,MAAM,IAAI,oBAAY,CAClB,wEAAwE,CAC3E,CAAA;QAEL,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,IAAa;QACd,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;QACpD,IACI,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,SAAS;YACrC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YAE9B,MAAM,IAAI,oBAAY,CAClB,wEAAwE,CAC3E,CAAA;QAEL,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,QAAQ,CAAC,KAAa;QAClB,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,KAAK,CAAA;QAEnC,OAAO,IAAI,CAAA;IACf,CAAC;IA6BD;;OAEG;IACH,OAAO,CACH,QAcqB,EACrB,WAA2B,EAC3B,UAAqB;QAErB,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACtC,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,WAAW,CAAA;QAC5C,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,UAAU,CAAA;QAC1C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,QAAkC;QAC1C,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACtC,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,WAAW;QACP,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAA;QACrC,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,YAAY;YAC5C,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAE/C,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,KAAK,CAAA;QACtC,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC5C,IAAI,sBAAsB,GAAY,KAAK,CAAA;QAC3C,IAAI,CAAC;YACD,sCAAsC;YACtC,IACI,IAAI,CAAC,aAAa,CAAC,cAAc,KAAK,IAAI;gBAC1C,WAAW,CAAC,mBAAmB,KAAK,KAAK,EAC3C,CAAC;gBACC,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;gBACpC,sBAAsB,GAAG,IAAI,CAAA;YACjC,CAAC;YAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;YAEtD,qCAAqC;YACrC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAA;YACzC,CAAC;YAED,OAAO,OAAO,CAAA;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,wCAAwC;YACxC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACD,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAA;gBAC3C,CAAC;gBAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC9B,CAAC;YACD,MAAM,KAAK,CAAA;QACf,CAAC;gBAAS,CAAC;YACP,IAAI,WAAW,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnC,wCAAwC;gBACxC,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;YAC/B,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB;QAInB,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC5C,IAAI,sBAAsB,GAAY,KAAK,CAAA;QAC3C,IAAI,CAAC;YACD,sCAAsC;YACtC,IACI,IAAI,CAAC,aAAa,CAAC,cAAc,KAAK,IAAI;gBAC1C,WAAW,CAAC,mBAAmB,KAAK,KAAK,EAC3C,CAAC;gBACC,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;gBACpC,sBAAsB,GAAG,IAAI,CAAA;YACjC,CAAC;YAED,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAA;YACrC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAAC,WAAW,CAAC,CAAA;YAEpE,qCAAqC;YACrC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAA;YACzC,CAAC;YAED,OAAO,OAAO,CAAA;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,wCAAwC;YACxC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACD,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAA;gBAC3C,CAAC;gBAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC9B,CAAC;YACD,MAAM,KAAK,CAAA;QACf,CAAC;gBAAS,CAAC;YACP,IAAI,WAAW,KAAK,IAAI,CAAC,WAAW;gBAChC,wCAAwC;gBACxC,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QACnC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM;QACR,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC9C,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAQ,CAAA;QAEzC,IACI,MAAM;YACN,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,YAAY;YAC5C,IAAI,CAAC,aAAa,CAAC,WAAW,EAChC,CAAC;YACC,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAA;YAEvD,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,YAAY,IAAI,EAAE,CAAC;gBACjD,MAAM,aAAa,GACf,QAAQ,CAAC,gBAAiB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,CAAC,6BAA6B;gBACnF,IACI,aAAa,CAAC,OAAO,EAAE;oBACvB,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE;oBAExC,MAAM,IAAI,uEAAkC,CACxC,QAAQ,CAAC,IAAI,EACb,IAAI,CAAC,aAAa,CAAC,WAAW,EAC9B,aAAa,CAChB,CAAA;YACT,CAAC;iBAAM,CAAC;gBACJ,MAAM,aAAa,GACf,QAAQ,CAAC,aAAc,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,CAAC,6BAA6B;gBAChF,IAAI,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,WAAW;oBAChD,MAAM,IAAI,uEAAkC,CACxC,QAAQ,CAAC,IAAI,EACb,IAAI,CAAC,aAAa,CAAC,WAAW,EAC9B,aAAa,CAChB,CAAA;YACT,CAAC;QACL,CAAC;QAED,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACvB,OAAO,IAAI,CAAA;QACf,CAAC;QACD,OAAO,MAAM,CAAA;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QACd,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,EAAE,CAAA;QAElC,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,IAAI,yCAAmB,CACzB,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,MAAM,EACpC,IAAI,CAAC,aAAa,CAAC,UAAU,CAChC,CAAA;QACL,CAAC;QAED,OAAO,MAAM,CAAA;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,YAAY;YAC5C,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAE/C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC9C,OAAO,OAAO,CAAC,QAAQ,CAAA;IAC3B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,QAAQ;QACV,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,YAAY;YAC5C,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAE/C,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC5C,IAAI,sBAAsB,GAAY,KAAK,CAAA;QAC3C,IAAI,CAAC;YACD,sCAAsC;YACtC,IACI,IAAI,CAAC,aAAa,CAAC,cAAc,KAAK,IAAI;gBAC1C,WAAW,CAAC,mBAAmB,KAAK,KAAK,EAC3C,CAAC;gBACC,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;gBACpC,sBAAsB,GAAG,IAAI,CAAA;YACjC,CAAC;YAED,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,KAAK,CAAA;YACtC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAA;YAEzD,qCAAqC;YACrC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAA;YACzC,CAAC;YAED,OAAO,OAAO,CAAA;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,wCAAwC;YACxC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACD,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAA;gBAC3C,CAAC;gBAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC9B,CAAC;YACD,MAAM,KAAK,CAAA;QACf,CAAC;gBAAS,CAAC;YACP,IAAI,WAAW,KAAK,IAAI,CAAC,WAAW;gBAChC,wCAAwC;gBACxC,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QACnC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,SAAS;QACX,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,YAAY;YAC5C,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAE/C,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC5C,IAAI,sBAAsB,GAAY,KAAK,CAAA;QAC3C,IAAI,CAAC;YACD,sCAAsC;YACtC,IACI,IAAI,CAAC,aAAa,CAAC,cAAc,KAAK,IAAI;gBAC1C,WAAW,CAAC,mBAAmB,KAAK,KAAK,EAC3C,CAAC;gBACC,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;gBACpC,sBAAsB,GAAG,IAAI,CAAA;YACjC,CAAC;YAED,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,KAAK,CAAA;YACtC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAA;YAE1D,qCAAqC;YACrC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAA;YACzC,CAAC;YAED,OAAO,OAAO,CAAA;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,wCAAwC;YACxC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACD,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAA;gBAC3C,CAAC;gBAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC9B,CAAC;YACD,MAAM,KAAK,CAAA;QACf,CAAC;gBAAS,CAAC;YACP,IAAI,WAAW,KAAK,IAAI,CAAC,WAAW;gBAChC,wCAAwC;gBACxC,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QACnC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,eAAe;QACjB,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,YAAY;YAC5C,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAE/C,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC5C,IAAI,sBAAsB,GAAY,KAAK,CAAA;QAC3C,IAAI,CAAC;YACD,sCAAsC;YACtC,IACI,IAAI,CAAC,aAAa,CAAC,cAAc,KAAK,IAAI;gBAC1C,WAAW,CAAC,mBAAmB,KAAK,KAAK,EAC3C,CAAC;gBACC,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;gBACpC,sBAAsB,GAAG,IAAI,CAAA;YACjC,CAAC;YAED,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAA;YACrC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAC1D,WAAW,CACd,CAAA;YACD,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,KAAK,CAAA;YACtC,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAA;YAC1C,yFAAyF;YACzF,2BAA2B;YAC3B,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAA;YACnE,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAA;YACvD,MAAM,OAAO,GAAuB,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;YAEpE,qCAAqC;YACrC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAA;YACzC,CAAC;YAED,OAAO,OAAO,CAAA;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,wCAAwC;YACxC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACD,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAA;gBAC3C,CAAC;gBAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC9B,CAAC;YACD,MAAM,KAAK,CAAA;QACf,CAAC;gBAAS,CAAC;YACP,IAAI,WAAW,KAAK,IAAI,CAAC,WAAW;gBAChC,wCAAwC;gBACxC,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QACnC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM;QACR,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,KAAK,CAAA;QACtC,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAA;QACtD,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC5C,IAAI,sBAAsB,GAAY,KAAK,CAAA;QAC3C,IAAI,CAAC;YACD,sCAAsC;YACtC,IACI,IAAI,CAAC,aAAa,CAAC,cAAc,KAAK,IAAI;gBAC1C,WAAW,CAAC,mBAAmB,KAAK,KAAK,EAC3C,CAAC;gBACC,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;gBACpC,sBAAsB,GAAG,IAAI,CAAA;YACjC,CAAC;YAED,MAAM,SAAS,GAAG,GAAG,EAAE;gBACnB,IAAI,WAAW,KAAK,IAAI,CAAC,WAAW;oBAChC,wCAAwC;oBACxC,OAAO,WAAW,CAAC,OAAO,EAAE,CAAA;gBAChC,OAAM;YACV,CAAC,CAAA;YACD,MAAM,OAAO,GAAG,WAAW,CAAC,MAAM,CAC9B,GAAG,EACH,UAAU,EACV,SAAS,EACT,SAAS,CACZ,CAAA;YAED,qCAAqC;YACrC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAA;YACzC,CAAC;YAED,OAAO,OAAO,CAAA;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,wCAAwC;YACxC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACD,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAA;gBAC3C,CAAC;gBAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC9B,CAAC;YACD,MAAM,KAAK,CAAA;QACf,CAAC;IACL,CAAC;IAkBD;;OAEG;IACH,KAAK,CACD,yBAAoD,EACpD,iBAA0B;QAE1B,IAAI,OAAO,yBAAyB,KAAK,SAAS,EAAE,CAAC;YACjD,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,yBAAyB,CAAA;QACxD,CAAC;aAAM,IAAI,OAAO,yBAAyB,KAAK,QAAQ,EAAE,CAAC;YACvD,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAA;YAC/B,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG,yBAAyB,CAAA;QAChE,CAAC;aAAM,IACH,OAAO,yBAAyB,KAAK,QAAQ;YAC7C,OAAO,yBAAyB,KAAK,QAAQ,EAC/C,CAAC;YACC,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAA;YAC/B,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,yBAAyB,CAAA;QAC1D,CAAC;QAED,IAAI,iBAAiB,EAAE,CAAC;YACpB,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG,iBAAiB,CAAA;QACxD,CAAC;QAED,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,MAAgC;QACtC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACvC,OAAO,IAAI,CAAA;IACf,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAElE,IAAI,CACV,SAA2B,EAC3B,gBAGgE,EAChE,SAAiB,EACjB,SAAkB,EAClB,UAA0B,EAC1B,aAAsB,EACtB,aAAuB,EACvB,WAA+B;QAE/B,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAClC,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,6BAAa,CACnC,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,aAAa,CACrB,CAAA;QACD,aAAa,CAAC,SAAS,GAAG,SAAS,CAAA;QACnC,aAAa,CAAC,WAAW,GAAG,WAAW,CAAA;QACvC,aAAa,CAAC,aAAa,GAAG,aAAa,CAAA;QAC3C,aAAa,CAAC,aAAa,GAAG,aAAa,CAAA;QAC3C,aAAa,CAAC,gBAAgB,GAAG,gBAAgB,CAAA,CAAC,eAAe;QACjE,aAAa,CAAC,SAAS,GAAG,SAAS,CAAA,CAAC,2BAA2B;QAC/D,yJAAyJ;QACzJ,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;QAErD,MAAM,qBAAqB,GAAG,aAAa,CAAC,QAAQ,CAAA;QACpD,IAAI,qBAAqB,EAAE,CAAC;YACxB,IACI,qBAAqB,CAAC,gBAAgB;gBACtC,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,EACjC,CAAC;gBACC,MAAM,qBAAqB,GAAG,GAAG,SAAS,IAAI,qBAAqB,CAAC,gBAAgB,CAAC,YAAY,UAAU,CAAA;gBAC3G,aAAa,CAAC,SAAS,GAAG,aAAa,CAAC,SAAS;oBAC7C,CAAC,CAAC,IAAI,aAAa,CAAC,SAAS,QAAQ,qBAAqB,EAAE;oBAC5D,CAAC,CAAC,GAAG,qBAAqB,EAAE,CAAA;YACpC,CAAC;YACD,2CAA2C;YAC3C,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;gBACjD,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,SAAS;gBACf,QAAQ,EAAE,qBAAqB;aAClC,CAAC,CAAA;YACF,IACI,aAAa,CAAC,QAAQ;gBACtB,aAAa,CAAC,QAAQ,CAAC,sBAAsB,EAC/C,CAAC;gBACC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;oBAC3B,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,aAAa,CAAC,aAAa;oBACjC,QAAQ,EAAE,aAAa,CAAC,QAAQ,CAAC,sBAAsB;iBAC1D,CAAC,CAAA;YACN,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,QAAQ,GAAW,EAAE,CAAA;YACzB,IAAI,OAAO,gBAAgB,KAAK,UAAU,EAAE,CAAC;gBACzC,MAAM,eAAe,GACjB,gBACH,CAAE,IAAuC,CAAC,QAAQ,EAAE,CAAC,CAAA;gBACtD,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC,CAAA;gBACnD,QAAQ,GAAG,eAAe,CAAC,QAAQ,EAAE,CAAA;YACzC,CAAC;iBAAM,CAAC;gBACJ,QAAQ,GAAG,gBAAgB,CAAA;YAC/B,CAAC;YACD,MAAM,UAAU,GACZ,OAAO,gBAAgB,KAAK,UAAU;gBACtC,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG;oBAClC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAA;YAC5C,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;gBACjD,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,SAAS;gBACf,SAAS,EACL,UAAU,KAAK,KAAK;oBAChB,CAAC,CAAE,gBAA2B;oBAC9B,CAAC,CAAC,SAAS;gBACnB,QAAQ,EAAE,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;aACvD,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACO,sBAAsB;QAC5B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS;YAC7B,MAAM,IAAI,oBAAY,CAClB,wEAAwE,CAC3E,CAAA;QAEL,qDAAqD;QAErD,MAAM,UAAU,GAAkB,EAAE,CAAA;QACpC,MAAM,eAAe,GAAkB,EAAE,CAAA;QAEzC,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAA;YACtD,UAAU,CAAC,IAAI,CACX,GAAG,IAAI,CAAC,+BAA+B,CACnC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,EACjC,QAAQ,CACX,CACJ,CAAA;YACD,eAAe,CAAC,IAAI,CAChB,GAAG,IAAI,CAAC,uBAAuB,CAC3B,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,EACjC,QAAQ,CACX,CACJ,CAAA;QACL,CAAC;QAED,yBAAyB;QACzB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAC/C,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,UAAU,CAAC,IAAI,CACX,GAAG,IAAI,CAAC,+BAA+B,CACnC,IAAI,CAAC,KAAK,CAAC,IAAK,EAChB,IAAI,CAAC,QAAQ,CAChB,CACJ,CAAA;gBACD,eAAe,CAAC,IAAI,CAChB,GAAG,IAAI,CAAC,uBAAuB,CAC3B,IAAI,CAAC,KAAK,CAAC,IAAK,EAChB,IAAI,CAAC,QAAQ,CAChB,CACJ,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAChD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CACnD,CAAA;gBACD,IAAI,YAAY,EAAE,CAAC;oBACf,UAAU,CAAC,IAAI,CAAC;wBACZ,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAK,CAAC,GAAG,IAAI;qBAClD,CAAC,CAAA;oBACF,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAClD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CACnD,CAAA;oBACD,eAAe,CAAC,IAAI,CAAC,cAAe,CAAC,CAAA;gBACzC,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAA;QAEF,wBAAwB;QACxB,IAAI,CAAC,aAAa,CAAC,OAAO;aACrB,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aAC1D,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAChB,UAAU,CAAC,IAAI,CAAC;YACZ,SAAS,EAAE,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,CAAC;YACtD,SAAS,EAAE,MAAM,CAAC,SAAS;SAC9B,CAAC,CACL,CAAA;QAEL,6DAA6D;QAC7D,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC;YAAE,UAAU,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAA;QAEhE,oBAAoB;QACpB,IAAI,QAAQ,GAAW,EAAE,CAAA;QACzB,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YAC9B,IAAI,yBAAW,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpD,QAAQ,GAAG,eAAe,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,CAAA;YAC5D,CAAC;QACL,CAAC;QAED,2BAA2B;QAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO;aACnC,MAAM,CACH,CAAC,KAAK,EAAE,EAAE,CACN,KAAK,CAAC,IAAI,KAAK,MAAM;YACrB,CAAC,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,QAAQ,CAAC,CAC1C;aACA,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YACX,IAAI,KAAK,CAAC,QAAQ;gBACd,OAAO,KAAK,CAAC,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YAEzD,OAAO,CACH,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,SAAU,CAAC;gBACnC,GAAG;gBACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAC1B,CAAA;QACL,CAAC,CAAC,CAAA;QAEN,MAAM,MAAM,GAAG,IAAI,CAAC,8BAA8B,EAAE,CAAA;QACpD,MAAM,SAAS,GAAG,UAAU;aACvB,GAAG,CACA,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,SAAS;YAChB,CAAC,MAAM,CAAC,SAAS;gBACb,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;gBACxC,CAAC,CAAC,EAAE,CAAC,CAChB;aACA,IAAI,CAAC,IAAI,CAAC,CAAA;QAEf,OAAO,CACH,MAAM;YACN,SAAS;YACT,QAAQ;YACR,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;YAChB,IAAI,CAAC,yBAAyB,EAAE;YAChC,QAAQ,CACX,CAAA;IACL,CAAC;IAED;;OAEG;IACO,8BAA8B;QACpC,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,GACxD,IAAI,CAAC,aAAa,CAAA;QACtB,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAA;QAElC,IAAI,MAAM,GAAG,SAAS,CAAA;QAEtB,IAAI,gBAAgB,GAAG,CAAC,EAAE,CAAC;YACvB,IAAI,yBAAW,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpC,MAAM,IAAI,0BAA0B,IAAI,CAAC,aAAa,CAAC,gBAAgB,OAAO,CAAA;YAClF,CAAC;QACL,CAAC;QAED,IACI,yBAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC;YACpC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAC7B,CAAC;YACC,MAAM,mBAAmB,GAAG,gBAAgB;iBACvC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;iBAC1C,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,MAAM,GAAG,uBAAuB,mBAAmB,IAAI,CAAA;QAC3D,CAAC;aAAM,IAAI,cAAc,EAAE,CAAC;YACxB,MAAM,GAAG,kBAAkB,CAAA;QAC/B,CAAC;QAED,OAAO,MAAM,CAAA;IACjB,CAAC;IAED;;OAEG;IACO,oBAAoB;QAC1B,YAAY;QACZ,0BAA0B;QAC1B,oBAAoB;QACpB,uDAAuD;QACvD,8BAA8B;QAC9B,wBAAwB;QACxB,mDAAmD;QAEnD,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC7D,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAA;YAClC,MAAM,oBAAoB,GAAG,QAAQ,CAAC,SAAS,CAAA;YAC/C,MAAM,qBAAqB,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAA;YACjD,IAAI,iBAAiB,GAAG,QAAQ,CAAC,SAAS;gBACtC,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC,SAAS,GAAG,GAAG;gBACrC,CAAC,CAAC,EAAE,CAAA;YACR,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAA;YAExC,sGAAsG;YACtG,0FAA0F;YAC1F,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,MAAM,eAAe,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ;oBAC3C,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ;oBACzB,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAA;gBAC7C,OAAO,CACH,GAAG;oBACH,QAAQ,CAAC,SAAS;oBAClB,QAAQ;oBACR,eAAe;oBACf,GAAG;oBACH,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC;oBAClC,IAAI,CAAC,yBAAyB,EAAE;oBAChC,CAAC,QAAQ,CAAC,SAAS;wBACf,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,SAAS,CAAC;wBACxD,CAAC,CAAC,EAAE,CAAC,CACZ,CAAA;YACL,CAAC;YAED,sCAAsC;YACtC,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;gBACnD,sEAAsE;gBACtE,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAW;qBACjC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;oBAChB,OAAO,CACH,qBAAqB;wBACrB,GAAG;wBACH,UAAU,CAAC,gBAAiB,CAAC,YAAY;wBACzC,GAAG;wBACH,WAAW;wBACX,GAAG;wBACH,QAAQ,CAAC,YAAY;wBACrB,GAAG;wBACH,UAAU,CAAC,gBAAiB,CAAC,YAAY,CAC5C,CAAA;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,OAAO,CAAC,CAAA;gBAElB,OAAO,CACH,GAAG;oBACH,QAAQ,CAAC,SAAS;oBAClB,QAAQ;oBACR,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC;oBACvC,GAAG;oBACH,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC;oBAClC,IAAI,CAAC,yBAAyB,EAAE;oBAChC,MAAM;oBACN,IAAI,CAAC,oBAAoB,CAAC,SAAS,GAAG,iBAAiB,CAAC,CAC3D,CAAA;YACL,CAAC;iBAAM,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;gBAC7D,8DAA8D;gBAC9D,MAAM,SAAS,GAAG,QAAQ;qBACrB,eAAgB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;oBAC7C,IACI,QAAQ,CAAC,qBAAqB,CAAC,SAAS;wBACpC,cAAc;wBAClB,QAAQ,CAAC,qBAAqB,CAAC,mBAAmB,EACpD,CAAC;wBACC,iBAAiB;4BACb,OAAO;gCACP,qBAAqB;gCACrB,GAAG;gCACH,QAAQ,CAAC,qBAAqB;qCACzB,mBAAmB,CAAC,YAAY;gCACrC,IAAI;gCACJ,QAAQ,CAAC,qBAAqB;qCACzB,kBAAkB;gCACvB,GAAG,CAAA;oBACX,CAAC;oBAED,OAAO,CACH,qBAAqB;wBACrB,GAAG;wBACH,QAAQ,CAAC,eAAgB,CAAC,YAAY;wBACtC,GAAG;wBACH,UAAU,CAAC,gBAAiB,CAAC,YAAY;wBACzC,GAAG;wBACH,WAAW;wBACX,GAAG;wBACH,UAAU,CAAC,gBAAiB,CAAC,YAAY,CAC5C,CAAA;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,OAAO,CAAC,CAAA;gBAElB,IAAI,CAAC,SAAS;oBACV,MAAM,IAAI,oBAAY,CAClB,YAAY,QAAQ,CAAC,cAAc,CAAC,IAAI,IAAI,QAAQ,CAAC,YAAY,8BAA8B,CAClG,CAAA;gBAEL,OAAO,CACH,GAAG;oBACH,QAAQ,CAAC,SAAS;oBAClB,QAAQ;oBACR,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC;oBACvC,GAAG;oBACH,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC;oBAClC,IAAI,CAAC,yBAAyB,EAAE;oBAChC,MAAM;oBACN,IAAI,CAAC,oBAAoB,CAAC,SAAS,GAAG,iBAAiB,CAAC,CAC3D,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,qBAAqB;gBACrB,MAAM,iBAAiB,GACnB,QAAQ,CAAC,sBAAuB,CAAC,SAAS,CAAA;gBAE9C,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAA;gBAC5C,IAAI,iBAAiB,GAAG,EAAE,EACtB,oBAAoB,GAAG,EAAE,CAAA;gBAE7B,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;oBACpB,iBAAiB,GAAG,QAAQ,CAAC,WAAW;yBACnC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;wBAChB,yCAAyC;wBACzC,OAAO,CACH,aAAa;4BACb,GAAG;4BACH,UAAU,CAAC,YAAY;4BACvB,GAAG;4BACH,WAAW;4BACX,GAAG;4BACH,UAAU,CAAC,gBAAiB,CAAC,YAAY,CAC5C,CAAA;oBACL,CAAC,CAAC;yBACD,IAAI,CAAC,OAAO,CAAC,CAAA;oBAElB,oBAAoB,GAAG,QAAQ,CAAC,kBAAkB;yBAC7C,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;wBAChB,iDAAiD;wBACjD,OAAO,CACH,qBAAqB;4BACrB,GAAG;4BACH,UAAU,CAAC,gBAAiB,CAAC,YAAY;4BACzC,GAAG;4BACH,aAAa;4BACb,GAAG;4BACH,UAAU,CAAC,YAAY,CAC1B,CAAA;oBACL,CAAC,CAAC;yBACD,IAAI,CAAC,OAAO,CAAC,CAAA;gBACtB,CAAC;qBAAM,CAAC;oBACJ,iBAAiB,GAAG,QAAQ;yBACvB,eAAgB,CAAC,kBAAkB,CAAC,GAAG,CACpC,CAAC,UAAU,EAAE,EAAE;wBACX,iDAAiD;wBACjD,OAAO,CACH,aAAa;4BACb,GAAG;4BACH,UAAU,CAAC,YAAY;4BACvB,GAAG;4BACH,WAAW;4BACX,GAAG;4BACH,UAAU,CAAC,gBAAiB,CAAC,YAAY,CAC5C,CAAA;oBACL,CAAC,CACJ;yBACA,IAAI,CAAC,OAAO,CAAC,CAAA;oBAElB,oBAAoB,GAAG,QAAQ;yBAC1B,eAAgB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;wBAC7C,yCAAyC;wBACzC,OAAO,CACH,qBAAqB;4BACrB,GAAG;4BACH,UAAU,CAAC,gBAAiB,CAAC,YAAY;4BACzC,GAAG;4BACH,aAAa;4BACb,GAAG;4BACH,UAAU,CAAC,YAAY,CAC1B,CAAA;oBACL,CAAC,CAAC;yBACD,IAAI,CAAC,OAAO,CAAC,CAAA;gBACtB,CAAC;gBAED,OAAO,CACH,GAAG;oBACH,QAAQ,CAAC,SAAS;oBAClB,QAAQ;oBACR,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC;oBACpC,GAAG;oBACH,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;oBAC1B,IAAI,CAAC,yBAAyB,EAAE;oBAChC,MAAM;oBACN,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC;oBAC5C,GAAG;oBACH,QAAQ,CAAC,SAAS;oBAClB,QAAQ;oBACR,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC;oBACvC,GAAG;oBACH,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC;oBAClC,IAAI,CAAC,yBAAyB,EAAE;oBAChC,MAAM;oBACN,IAAI,CAAC,oBAAoB,CACrB,oBAAoB,GAAG,iBAAiB,CAC3C,CACJ,CAAA;YACL,CAAC;QACL,CAAC,CAAC,CAAA;QAEF,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC1B,CAAC;IAED;;OAEG;IACO,uBAAuB;QAC7B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM;YACnE,OAAO,EAAE,CAAA;QACb,OAAO,CACH,YAAY;YACZ,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CACpE,CAAA;IACL,CAAC;IAED;;OAEG;IACO,uBAAuB;QAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAA;QAC/C,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,CAAA;QAEjD,OAAO,CACH,YAAY;YACZ,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAChB,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBAChB,MAAM,UAAU,GACZ,OAAO,QAAQ,CAAC,UAAU,CAAC,KAAK,QAAQ;oBACpC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;oBACtB,CAAC,CAAE,QAAQ,CAAC,UAAU,CAAS,CAAC,KAAK;wBACnC,GAAG;wBACF,QAAQ,CAAC,UAAU,CAAS,CAAC,KAAK,CAAA;gBAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAC7C,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,KAAK,UAAU,CACpC,CAAA;gBACD,IACI,SAAS;oBACT,CAAC,SAAS,CAAC,SAAS;oBACpB,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAChC,CAAC;oBACC,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;oBAC3C,MAAM,SAAS,GAAG,aAAa,CAAC,CAAC,CAAC,CAAA;oBAClC,MAAM,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;oBACrD,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CACzC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CACtC,CAAA;oBACD,IAAI,KAAK,EAAE,CAAC;wBACR,MAAM,MAAM,GACR,KAAK,CAAC,QAAQ,CAAC,0BAA0B,CACrC,YAAY,CACf,CAAA;wBACL,IAAI,MAAM,EAAE,CAAC;4BACT,MAAM,UAAU,GAAG,yBAAW,CAAC,UAAU,CACrC,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,SAAS,EACT,MAAM,CAAC,YAAY,CACtB,CAAA;4BACD,OAAO,CACH,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,GAAG,GAAG,UAAU,CAC7C,CAAA;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,OAAO,CACH,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,GAAG,GAAG,GAAG,UAAU,CAC3D,CAAA;YACL,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAClB,CAAA;IACL,CAAC;IAED;;OAEG;IACO,2BAA2B;QACjC,oHAAoH;QACpH,wHAAwH;QACxH,IAAI,MAAM,GAAuB,IAAI,CAAC,aAAa,CAAC,MAAM,EACtD,KAAK,GAAuB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAA;QACxD,IACI,CAAC,MAAM;YACP,CAAC,KAAK;YACN,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAChD,CAAC;YACC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAA;YAChC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAA;QACnC,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClD,sFAAsF;YACtF,qFAAqF;YACrF,oFAAoF;YACpF,+EAA+E;YAC/E,yCAAyC;YACzC,IAAI,MAAM,GAAG,EAAE,CAAA;YACf,IACI,CAAC,KAAK,IAAI,MAAM,CAAC;gBACjB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,MAAM,IAAI,CAAC,EACzD,CAAC;gBACC,MAAM,GAAG,yBAAyB,CAAA;YACtC,CAAC;YAED,IAAI,KAAK,IAAI,MAAM;gBACf,OAAO,CACH,MAAM;oBACN,UAAU;oBACV,MAAM;oBACN,mBAAmB;oBACnB,KAAK;oBACL,YAAY,CACf,CAAA;YACL,IAAI,KAAK;gBACL,OAAO,CACH,MAAM,GAAG,4BAA4B,GAAG,KAAK,GAAG,YAAY,CAC/D,CAAA;YACL,IAAI,MAAM;gBAAE,OAAO,MAAM,GAAG,UAAU,GAAG,MAAM,GAAG,OAAO,CAAA;QAC7D,CAAC;aAAM,IACH,yBAAW,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;YACjD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,cAAc;YACtD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK;YAC7C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,EACnD,CAAC;YACC,IAAI,KAAK,IAAI,MAAM;gBAAE,OAAO,SAAS,GAAG,KAAK,GAAG,UAAU,GAAG,MAAM,CAAA;YACnE,IAAI,KAAK;gBAAE,OAAO,SAAS,GAAG,KAAK,CAAA;YACnC,IAAI,MAAM;gBAAE,MAAM,IAAI,yEAAmC,EAAE,CAAA;QAC/D,CAAC;aAAM,IAAI,yBAAW,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5D,IAAI,KAAK,IAAI,MAAM;gBAAE,OAAO,SAAS,GAAG,KAAK,GAAG,UAAU,GAAG,MAAM,CAAA;YACnE,IAAI,KAAK;gBAAE,OAAO,SAAS,GAAG,KAAK,CAAA;YACnC,IAAI,MAAM;gBAAE,OAAO,mBAAmB,GAAG,MAAM,CAAA;QACnD,CAAC;aAAM,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC1D,IAAI,KAAK,IAAI,MAAM;gBACf,OAAO,CACH,UAAU;oBACV,MAAM;oBACN,mBAAmB;oBACnB,KAAK;oBACL,YAAY,CACf,CAAA;YACL,IAAI,KAAK;gBAAE,OAAO,cAAc,GAAG,KAAK,GAAG,YAAY,CAAA;YACvD,IAAI,MAAM;gBAAE,OAAO,UAAU,GAAG,MAAM,GAAG,OAAO,CAAA;QACpD,CAAC;aAAM,CAAC;YACJ,IAAI,KAAK,IAAI,MAAM;gBAAE,OAAO,SAAS,GAAG,KAAK,GAAG,UAAU,GAAG,MAAM,CAAA;YACnE,IAAI,KAAK;gBAAE,OAAO,SAAS,GAAG,KAAK,CAAA;YACnC,IAAI,MAAM;gBAAE,OAAO,UAAU,GAAG,MAAM,CAAA;QAC1C,CAAC;QAED,OAAO,EAAE,CAAA;IACb,CAAC;IAED;;;;;;;OAOG;IACK,yBAAyB;QAC7B,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClD,QAAQ,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;gBAClC,KAAK,kBAAkB;oBACnB,OAAO,2BAA2B,CAAA;gBACtC,KAAK,mBAAmB;oBACpB,OAAO,0BAA0B,CAAA;gBACrC,KAAK,YAAY;oBACb,OAAO,gBAAgB,CAAA;YAC/B,CAAC;QACL,CAAC;QAED,OAAO,EAAE,CAAA;IACb,CAAC;IAED;;OAEG;IACO,oBAAoB;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAA;QAErC,IAAI,gBAAgB,GAAG,EAAE,CAAA;QAEzB,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;YAChC,IACI,CAAC,CACG,yBAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC;gBACpC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,aAAa,CACxC,EACH,CAAC;gBACC,MAAM,IAAI,oBAAY,CAClB,8CAA8C,CACjD,CAAA;YACL,CAAC;YACD,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3C,MAAM,IAAI,oBAAY,CAAC,qCAAqC,CAAC,CAAA;YACjE,CAAC;YACD,gBAAgB,GAAG,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACxE,CAAC;QAED,IAAI,gBAAgB,GAAG,EAAE,CAAA;QACzB,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC3C,gBAAgB,GAAG,SAAS,CAAA;QAChC,CAAC;aAAM,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YACvD,gBAAgB,GAAG,cAAc,CAAA;QACrC,CAAC;QACD,QAAQ,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YAClC,KAAK,kBAAkB;gBACnB,IACI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO;oBAC/B,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,cAAc,EACxC,CAAC;oBACC,IACI,yBAAW,CAAC,yBAAyB,CAAC,MAAM,EAAE,OAAO,CAAC,EACxD,CAAC;wBACC,OAAO,CACH,YAAY,GAAG,gBAAgB,GAAG,gBAAgB,CACrD,CAAA;oBACL,CAAC;yBAAM,CAAC;wBACJ,OAAO,qBAAqB,CAAA;oBAChC,CAAC;gBACL,CAAC;qBAAM,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC3C,OAAO,qBAAqB,CAAA;gBAChC,CAAC;qBAAM,IAAI,yBAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC9C,OAAO,YAAY,GAAG,gBAAgB,GAAG,gBAAgB,CAAA;gBAC7D,CAAC;qBAAM,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAC1C,OAAO,aAAa,CAAA;gBACxB,CAAC;qBAAM,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oBACzC,OAAO,EAAE,CAAA;gBACb,CAAC;qBAAM,CAAC;oBACJ,MAAM,IAAI,uEAAkC,EAAE,CAAA;gBAClD,CAAC;YACL,KAAK,mBAAmB;gBACpB,IACI,yBAAW,CAAC,aAAa,CAAC,MAAM,CAAC;oBACjC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,cAAc;oBACtC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ,EAClC,CAAC;oBACC,OAAO,aAAa,GAAG,gBAAgB,CAAA;gBAC3C,CAAC;qBAAM,IACH,yBAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC;oBACpC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,aAAa,EACvC,CAAC;oBACC,OAAO,aAAa,GAAG,gBAAgB,GAAG,gBAAgB,CAAA;gBAC9D,CAAC;qBAAM,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oBACzC,OAAO,EAAE,CAAA;gBACb,CAAC;qBAAM,CAAC;oBACJ,MAAM,IAAI,uEAAkC,EAAE,CAAA;gBAClD,CAAC;YACL,KAAK,2BAA2B;gBAC5B,IAAI,yBAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;oBACvC,OAAO,aAAa,GAAG,gBAAgB,GAAG,cAAc,CAAA;gBAC5D,CAAC;qBAAM,IAAI,yBAAW,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC3C,OAAO,yBAAyB,CAAA;gBACpC,CAAC;qBAAM,CAAC;oBACJ,MAAM,IAAI,uEAAkC,EAAE,CAAA;gBAClD,CAAC;YACL,KAAK,2BAA2B;gBAC5B,IACI,yBAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC;oBACpC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,aAAa,EACvC,CAAC;oBACC,OAAO,aAAa,GAAG,gBAAgB,GAAG,SAAS,CAAA;gBACvD,CAAC;qBAAM,IAAI,yBAAW,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC3C,OAAO,oBAAoB,CAAA;gBAC/B,CAAC;qBAAM,CAAC;oBACJ,MAAM,IAAI,uEAAkC,EAAE,CAAA;gBAClD,CAAC;YACL,KAAK,mBAAmB;gBACpB,IACI,yBAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC;oBACpC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,aAAa,EACvC,CAAC;oBACC,OAAO,CACH,oBAAoB;wBACpB,gBAAgB;wBAChB,gBAAgB,CACnB,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,MAAM,IAAI,uEAAkC,EAAE,CAAA;gBAClD,CAAC;YACL,KAAK,eAAe;gBAChB,IAAI,yBAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;oBACvC,OAAO,CACH,gBAAgB,GAAG,gBAAgB,GAAG,gBAAgB,CACzD,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,MAAM,IAAI,uEAAkC,EAAE,CAAA;gBAClD,CAAC;YACL;gBACI,OAAO,EAAE,CAAA;QACjB,CAAC;IACL,CAAC;IAED;;OAEG;IACO,sBAAsB;QAC5B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM;YACjE,OAAO,EAAE,CAAA;QACb,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO;aACxC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACnB,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,KAAK;oBACN,OAAO,CACH,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;wBACzB,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,CAAC,CAC9C,CAAA;gBACL,KAAK,IAAI;oBACL,OAAO,CACH,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;wBACxB,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,CAAC,CAC9C,CAAA;gBACL;oBACI,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;YAC1D,CAAC;QACL,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC,CAAA;QAEd,IAAI,CAAC,UAAU,CAAC,MAAM;YAAE,OAAO,EAAE,CAAA;QACjC,OAAO,UAAU,GAAG,UAAU,CAAA;IAClC,CAAC;IAES,+BAA+B,CACrC,SAAiB,EACjB,QAAwB;QAExB,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAChD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,KAAK,SAAS,CAC7C,CAAA;QAED,MAAM,OAAO,GAAqB,EAAE,CAAA;QACpC,IAAI,YAAY,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CACR,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CACtB,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,KAAK,IAAI,CACvC,CACJ,CAAA;QACL,CAAC;QACD,OAAO,CAAC,IAAI,CACR,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;YAClC,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAClC,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,SAAS;gBAChB,SAAS,GAAG,GAAG,GAAG,MAAM,CAAC,YAAY,CAC5C,CAAA;QACL,CAAC,CAAC,CACL,CAAA;QAED,2GAA2G;QAC3G,4HAA4H;QAC5H,oDAAoD;QACpD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YACpB,0HAA0H;YAC1H,OAAO,EAAE,CAAA;QAEb,MAAM,yBAAyB,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW;YAC5D,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAC1B,CAAC,aAAa,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAC3D;YACH,CAAC,CAAC,EAAE,CAAA;QACR,MAAM,UAAU,GAAG,CAAC,GAAG,OAAO,EAAE,GAAG,yBAAyB,CAAC,CAAA;QAC7D,MAAM,YAAY,GAAkB,EAAE,CAAA;QAEtC,MAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;QAC/C,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAC1B,IAAI,aAAa,GACb,gBAAgB,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;YAE7D,IAAI,MAAM,CAAC,iBAAiB,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBAC3C,aAAa,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAA;YACzD,CAAC;YAED,IACI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EACjE,CAAC;gBACC,IACI,yBAAW,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;oBACjD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,cAAc,EACxD,CAAC;oBACC,MAAM,SAAS,GACX,IAAI,CAAC,UAAU,CAAC,MAGnB,CAAC,OAAO,CAAC,oBAAoB,CAAA;oBAC9B,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAA;oBACjD,aAAa,GAAG,GAAG,MAAM,IAAI,aAAa,GAAG,CAAA;gBACjD,CAAC;gBAED,IAAI,yBAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;oBACpD,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;wBACnB,gDAAgD;wBAChD,aAAa,GAAG,gBAAgB,aAAa,KAAK,MAAM,CAAC,SAAS,SAAS,CAAA;oBAC/E,CAAC;yBAAM,CAAC;wBACJ,aAAa,GAAG,gBAAgB,aAAa,SAAS,CAAA;oBAC1D,CAAC;gBACL,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO;oBAC/C,aAAa,GAAG,GAAG,aAAa,aAAa,CAAA;YACrD,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAChD,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,SAAS,KAAK,SAAS,GAAG,GAAG,GAAG,MAAM,CAAC,YAAY,CACjE,CAAA;YACD,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;gBACpB,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;oBAC7B,YAAY,CAAC,IAAI,CAAC;wBACd,SAAS,EAAE,aAAa;wBACxB,SAAS,EAAE,SAAS,CAAC,SAAS;4BAC1B,CAAC,CAAC,SAAS,CAAC,SAAS;4BACrB,CAAC,CAAC,yBAAW,CAAC,UAAU,CAClB,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,SAAS,EACT,MAAM,CAAC,YAAY,CACtB;wBACP,4FAA4F;wBAC5F,OAAO,EAAE,SAAS,CAAC,OAAO;qBAC7B,CAAC,CAAA;gBACN,CAAC,CAAC,CAAA;YACN,CAAC;iBAAM,CAAC;gBACJ,YAAY,CAAC,IAAI,CAAC;oBACd,SAAS,EAAE,aAAa;oBACxB,SAAS,EAAE,yBAAW,CAAC,UAAU,CAC7B,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,SAAS,EACT,MAAM,CAAC,YAAY,CACtB;oBACD,4FAA4F;oBAC5F,OAAO,EAAE,YAAY;iBACxB,CAAC,CAAA;YACN,CAAC;QACL,CAAC,CAAC,CAAA;QACF,OAAO,YAAY,CAAA;IACvB,CAAC;IAES,uBAAuB,CAC7B,SAAiB,EACjB,QAAwB;QAExB,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAC9C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,KAAK,SAAS,CAC7C,CAAA;QACD,IAAI,UAAU;YAAE,OAAO,CAAC,UAAU,CAAC,CAAA;QAEnC,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;YAChD,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CACxB,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,SAAS,KAAK,SAAS,GAAG,GAAG,GAAG,MAAM,CAAC,YAAY,CACjE,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAEO,sBAAsB;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI,CAAA,CAAC,6CAA6C;QAClG,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAA;QAEvD,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAA;QAC9C,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;QAE5C,4FAA4F;QAC5F,6DAA6D;QAC7D,IACI,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC;YAC9C,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC;YACpD,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,MAAM,KAAK,CAAC,EACzD,CAAC;YACC,OAAO,UAAU,CAAA;QACrB,CAAC;QAED,sFAAsF;QAEtF,IACI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,aAAa;YACrD,yBAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EACtD,CAAC;YACC,mFAAmF;YACnF,mEAAmE;YACnE,OAAO,CACH,iBAAiB;gBACjB,cAAc;qBACT,GAAG,CACA,CAAC,CAAC,EAAE,EAAE,CACF,GAAG,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,CACxD;qBACA,IAAI,CAAC,IAAI,CAAC;gBACf,IAAI,CACP,CAAA;QACL,CAAC;QAED,IAAI,yBAAW,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;YACpD,oFAAoF;YACpF,4CAA4C;YAC5C,OAAO,CACH,iBAAiB;gBACjB,cAAc;qBACT,GAAG,CACA,CAAC,CAAC,EAAE,EAAE,CACF,GAAG,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,CACxD;qBACA,IAAI,CAAC,IAAI,CAAC;gBACf,GAAG,CACN,CAAA;QACL,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClD,4EAA4E;YAC5E,yEAAyE;YACzE,2EAA2E;YAC3E,qFAAqF;YAErF,MAAM,iBAAiB,GAAG,cAAc;iBACnC,GAAG,CACA,CAAC,aAAa,EAAE,EAAE,CACd,GAAG,aAAa,IAAI,IAAI,CAAC,MAAM,CAC3B,aAAa,CAAC,YAAY,CAC7B,EAAE,CACV;iBACA,IAAI,CAAC,WAAW,CAAC,CAAA;YAEtB,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC9B,OAAO,kBAAkB,iBAAiB,IAAI,CAAA;YAClD,CAAC;YAED,OAAO,yBAAyB,iBAAiB,KAAK,CAAA;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACpD,0DAA0D;YAC1D,6FAA6F;YAE7F,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC9B,OAAO,kBAAkB,aAAa,IAAI,IAAI,CAAC,MAAM,CACjD,cAAc,CAAC,CAAC,CAAC,CAAC,YAAY,CACjC,IAAI,CAAA;YACT,CAAC;YAED,MAAM,iBAAiB,GAAG,cAAc;iBACnC,GAAG,CACA,CAAC,aAAa,EAAE,EAAE,CACd,QAAQ,aAAa,IAAI,IAAI,CAAC,MAAM,CAChC,aAAa,CAAC,YAAY,CAC7B,aAAa,CACrB;iBACA,IAAI,CAAC,WAAW,CAAC,CAAA;YACtB,OAAO,yBAAyB,iBAAiB,KAAK,CAAA;QAC1D,CAAC;QAED,wGAAwG;QACxG,uFAAuF;QACvF,+DAA+D;QAE/D,iGAAiG;QACjG,mEAAmE;QAEnE,OAAO,CACH,iBAAiB;YACjB,cAAc;iBACT,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC;iBAC7D,IAAI,CAAC,eAAe,CAAC;YAC1B,IAAI,CACP,CAAA;IACL,CAAC;IAES,KAAK,CAAC,iBAAiB,CAC7B,WAAwB;QAExB,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAE9C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,EAAE;aAC7B,OAAO,EAAE;aACT,OAAO,EAAE;aACT,MAAM,CAAC,SAAS,CAAC;aACjB,KAAK,CAAC,SAAS,CAAC;aAChB,IAAI,CAAC,SAAS,CAAC;aACf,IAAI,CAAC,SAAS,CAAC;aACf,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC;aACvB,SAAS,CAAC,sBAAsB,CAAC;aACjC,cAAc,CAAC,WAAW,CAAC,CAAA;QAEhC,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAAE,OAAO,CAAC,CAAA;QAE3D,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAA;IACtC,CAAC;IAES,KAAK,CAAC,kBAAkB,CAC9B,WAAwB;QAExB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU;aAChC,kBAAkB,EAAE;aACpB,SAAS,EAAE;aACX,MAAM,CAAC,GAAG,EAAE,YAAY,CAAC;aACzB,WAAW,CAAC,IAAI,CAAC;aACjB,KAAK,CAAC,CAAC,CAAC;aACR,cAAc,CAAC,WAAW,CAAC,CAAA;QAEhC,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAA;IAC7B,CAAC;IAES,gBAAgB;QACtB,mEAAmE;QACnE,0BAA0B;QAE1B,IAAI,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,WAAW,CAAC,oBAAoB,EAAE,CAAC;gBACxC,IAAI,CAAC,aAAa,CAAC,oBAAoB;oBACnC,IAAI,CAAC,WAAW,CAAC,oBAAoB,CAAA;YAC7C,CAAC;YAED,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;gBAC3B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;YAC1C,CAAC;YAED,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;gBAC/B,IAAI,CAAC,WAAW,EAAE,CAAA;YACtB,CAAC;YAED,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;gBAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;oBACjD,CAAC,CAAC,mBAAQ,CAAC,2BAA2B,CAChC,IAAI,CAAC,WAAW,CAAC,MAAkB,CACtC;oBACH,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAA;gBAE7B,IAAI,CAAC,WAAW,CACZ,MAAM,EACN,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,EACtC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI,CACrC,CAAA;YACL,CAAC;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC7B,CAAC;YAED,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;YACjB,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;gBAC7B,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;oBACvD,CAAC,CAAC,mBAAQ,CAAC,2BAA2B,CAChC,IAAI,CAAC,WAAW,CAAC,SAAS,CAC7B;oBACH,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAA;gBAEhC,IAAI,CAAC,cAAc,CACf,SAAS,EACT,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,QAAQ;oBACvC,CAAC,CAAE,IAAI,CAAC,WAAW,CAAC,MAAiC;oBACrD,CAAC,CAAC,SAAS,EACf,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,EACtC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI,CACrC,CAAA;gBACD,IACI,IAAI,CAAC,WAAW,CAAC,kBAAkB,KAAK,KAAK;oBAC7C,IAAI,CAAC,aAAa,CAAC,oBAAoB,KAAK,MAAM,EACpD,CAAC;oBACC,IAAI,CAAC,mBAAmB,CACpB,SAAS,EACT,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,QAAQ;wBACvC,CAAC,CAAE,IAAI,CAAC,WAAW;6BACZ,MAAiC;wBACxC,CAAC,CAAC,SAAS,EACf,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,EACtC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI,CACrC,CAAA;gBACL,CAAC;YACL,CAAC;YACD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBACtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAChC,CAAC;YAED,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;gBACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAC7B,IAAI,CAAC,WAAW,CAAC,KAAK,EACtB,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,EACtC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI,CACrC,CAAA;gBAED,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM;oBACtB,IAAI,CAAC,QAAQ,CACT,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG;wBAChC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,GAAG,GAAG;wBAC7B,CAAC,CAAC,IAAI,CAAC,UAAU,CACxB,CAAA,CAAC,iCAAiC;YAC3C,CAAC;YAED,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;gBACzB,IAAI,CAAC,UAAU,CACX,IAAI,CAAC,WAAW,CAAC,KAAK,EACtB,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,EACtC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI,CACrC,CAAA;YACL,CAAC;YAED,cAAc;YACd,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBACpB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBACxB,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;wBACjC,wBAAwB;wBACxB,EAAE;wBACF,WAAW;wBACX,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;4BACxB,IAAI,CAAC,kBAAkB,CACnB,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,EAC3D,IAAI,CAAC,KAAK,CACb,CAAA;wBACL,CAAC;6BAAM,CAAC;4BACJ,IAAI,CAAC,iBAAiB,CAClB,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,EAC3D,IAAI,CAAC,KAAK,CACb,CAAA;wBACL,CAAC;wBACD,IAAI;oBACR,CAAC;yBAAM,CAAC;wBACJ,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;4BACxB,IAAI,CAAC,SAAS,CACV,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,EAC3D,IAAI,CAAC,KAAK,CACb,CAAA;wBACL,CAAC;6BAAM,CAAC;4BACJ,IAAI,CAAC,QAAQ,CACT,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,EAC3D,IAAI,CAAC,KAAK,CACb,CAAA;wBACL,CAAC;oBACL,CAAC;oBAED,qBAAqB;oBACrB,2DAA2D;oBAC3D,+CAA+C;oBAC/C,oBAAoB;oBACpB,0BAA0B;oBAC1B,0DAA0D;oBAC1D,aAAa;oBACb,QAAQ;oBACR,IAAI;gBACR,CAAC,CAAC,CAAA;YACN,CAAC;YAED,gCAAgC;YAChC,iDAAiD;YACjD,IAAI;YAEJ,eAAe;YACf,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBACtC,mFAAmF;gBACnF,0CAA0C;gBAC1C,WAAW;gBACX,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;gBAChC,IAAI;YACR,CAAC;YAED,cAAc;YACd,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBACtC,mFAAmF;gBACnF,yCAAyC;gBACzC,WAAW;gBACX,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;gBAChC,IAAI;YACR,CAAC;YAED,wBAAwB;YACxB,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC7C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;YACtC,CAAC;iBAAM,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBACrD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;YACtC,CAAC;iBAAM,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACpD,IAAI,CAAC,KAAK,CACN,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,EACzB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,YAAY,CACtC,CAAA;YACL,CAAC;YAED,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;gBACxB,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ;oBAC9B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAC/C,CAAC,GAAG,EAAE,EAAE;wBACJ,IAAI,CAAC,QAAQ,CACT,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,QAAS,CAAC,GAAG,CAAC,EACrC,GAAG,CACN,CAAA;oBACL,CAAC,CACJ,CAAA;gBAEL,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;oBAC/B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAChD,CAAC,GAAG,EAAE,EAAE;wBACJ,IAAI,CAAC,SAAS,CACV,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,SAAU,CAAC,GAAG,CAAC,EACtC,GAAG,CACN,CAAA;oBACL,CAAC,CACJ,CAAA;gBAEL,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB;oBACvC,MAAM,CAAC,IAAI,CACP,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAC1C,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;wBACd,IAAI,CAAC,iBAAiB,CAClB,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,iBAAkB,CAAC,GAAG,CAAC,EAC9C,GAAG,CACN,CAAA;oBACL,CAAC,CAAC,CAAA;gBAEN,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB;oBACxC,MAAM,CAAC,IAAI,CACP,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAC3C,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;wBACd,IAAI,CAAC,kBAAkB,CACnB,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,kBAAmB,CAAC,GAAG,CAAC,EAC/C,GAAG,CACN,CAAA;oBACL,CAAC,CAAC,CAAA;YACV,CAAC;YAED,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;gBACxB,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;oBAC9C,IAAI,CAAC,OAAO,CACR,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAC1B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAChC,CAAA;gBACL,CAAC;qBAAM,IACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,kBAAkB;oBACjD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,mBAAmB;oBAClD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY;oBAC3C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;wBACtB,2BAA2B;oBAC/B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;wBACtB,2BAA2B;oBAC/B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,mBAAmB;oBAClD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,eAAe,EAChD,CAAC;oBACC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM;wBAC3C,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;4BACvC,MAAM,UAAU,GACZ,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;gCACtC,OAAO,CACH,KAAK,CAAC,QAAQ;qCACT,sBAAsB,KAAK,KAAK,CACxC,CAAA;4BACL,CAAC,CAAC,CAAA;4BACN,IAAI,CAAC,UAAU,EAAE,CAAC;gCACd,MAAM,IAAI,oBAAY,CAClB,IAAI,KAAK,6BAA6B,CACzC,CAAA;4BACL,CAAC;4BACD,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;wBACvC,CAAC,CAAC;wBACJ,CAAC,CAAC,SAAS,CAAA;oBACf,IAAI,CAAC,OAAO,CACR,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAC1B,SAAS,EACT,UAAU,CACb,CAAA;oBAED,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACjC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;oBACpD,CAAC;gBACL,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,KAAK,IAAI,EAAE,CAAC;gBAC5C,IAAI,CAAC,kBAAkB,EAAE,CAAA;YAC7B,CAAC;iBAAM,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe,KAAK,QAAQ,EAAE,CAAC;gBAC9D,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,eAAsB,CAAC,CAAA;YACpE,CAAC;YAED,IAAI,IAAI,CAAC,WAAW,CAAC,kBAAkB,KAAK,KAAK,EAAE,CAAC;gBAChD,mCAAgB,CAAC,kBAAkB,CAC/B,IAAI,EACJ,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI,EAClC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CACzC,CAAA;YACL,CAAC;YAED,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,KAAK,IAAI,EAAE,CAAC;gBACxC,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,IAAI,CAAA;YAC5C,CAAC;YAED,8BAA8B;YAC9B,yCAAyC;YACzC,4EAA4E;YAC5E,UAAU;YACV,IAAI;YAEJ,OAAO;YACP,qEAAqE;YACrE,wBAAwB;YACxB,IAAI;YAEJ,OAAO;YACP,0EAA0E;YAC1E,iCAAiC;YACjC,IAAI;QACR,CAAC;IACL,CAAC;IAEM,sBAAsB,CAAC,gBAAkC;QAC5D,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,4BAA4B,CACxC,WAAwB;QAExB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS;YAC7B,MAAM,IAAI,oBAAY,CAClB,sDAAsD,CACzD,CAAA;QAEL,IACI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,kBAAkB;YAC/C,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,mBAAmB;YACnD,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,2BAA2B;YAC3D,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,2BAA2B;YAC3D,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,mBAAmB;YACnD,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,eAAe,CAAC;YACpD,CAAC,WAAW,CAAC,mBAAmB;YAEhC,MAAM,IAAI,iFAAuC,EAAE,CAAA;QAEvD,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC/C,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAA;YACtD,IAAI,CAAC,QAAQ,CAAC,aAAa,IAAI,CAAC,QAAQ,CAAC,gBAAgB;gBACrD,MAAM,IAAI,mEAAgC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QACjE,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,mCAAgB,CACzC,IAAI,CAAC,UAAU,EACf,WAAW,EACX,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAC1C,CAAA;QACD,MAAM,mBAAmB,GAAG,IAAI,yCAAmB,CAC/C,IAAI,CAAC,UAAU,EACf,WAAW,EACX,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAC7C,CAAA;QACD,MAAM,6BAA6B,GAC/B,IAAI,mFAAwC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;QACpE,6BAA6B,CAAC,SAAS,EAAE,CAAA;QACzC,MAAM,gCAAgC,GAClC,IAAI,yFAA2C,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;QACvE,gCAAgC,CAAC,SAAS,EAAE,CAAA;QAE5C,IAAI,UAAU,GAAU,EAAE,EACtB,QAAQ,GAAU,EAAE,CAAA;QAExB,gGAAgG;QAChG,qDAAqD;QACrD,8CAA8C;QAC9C,4DAA4D;QAC5D,IACI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACpD,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAC9C,CAAC;YACC,6EAA6E;YAC7E,mEAAmE;YACnE,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,GACrB,IAAI,CAAC,yCAAyC,CAAC,eAAe,CAAC,CAAA;YACnE,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAA;YACtD,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAA;YAEvD,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,GAAG,CAC5C,CAAC,aAAa,EAAE,EAAE;gBACd,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAA;gBAClD,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAC3B,yBAAW,CAAC,UAAU,CAClB,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,aAAa,EACb,aAAa,CAAC,YAAY,CAC7B,CACJ,CAAA;gBACD,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;oBACtB,yEAAyE;oBACzE,QAAQ,CAAC,WAAW,CAAC,GAAG,KAAK,CAAA;gBAEjC,MAAM,KAAK,GAAG,yBAAW,CAAC,UAAU,CAChC,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,MAAM,GAAG,aAAa,EACtB,aAAa,CAAC,YAAY,CAC7B,CAAA;gBAED,OAAO,GAAG,aAAa,IAAI,WAAW,OAAO,IAAI,CAAC,MAAM,CACpD,KAAK,CACR,EAAE,CAAA;YACP,CAAC,CACJ,CAAA;YAED,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,EAAE,CAAA;YAElC,4EAA4E;YAC5E,MAAM,uBAAuB,GACzB,aAAa,CAAC,aAAa,CAAC,UAAU,CAAA;YAE1C,UAAU,GAAG,MAAM,IAAI,kBAAkB,CACrC,IAAI,CAAC,UAAU,EACf,WAAW,CACd;iBACI,MAAM,CAAC,YAAY,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;iBAC7C,SAAS,CAAC,OAAO,CAAC;iBAClB,IAAI,CACD,IAAI,aAAa;iBACZ,OAAO,EAAE;iBACT,eAAe,CAAC,KAAK,CAAC,CAAC,kHAAkH;iBACzI,QAAQ,EAAE,GAAG,EAClB,eAAe,CAClB;iBACA,eAAe,CAAC,uBAAuB,CAAC;iBACxC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;iBAC/B,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;iBAC9B,OAAO,CAAC,QAAQ,CAAC;iBACjB,KAAK,CACF,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO;gBAClD,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,aAAa;gBAC5C,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAC9B,IAAI,CAAC,aAAa,CAAC,aAAa,CACnC;iBACA,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;iBACnC,mBAAmB,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC;iBACxD,UAAU,EAAE,CAAA;YAEjB,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,IAAI,SAAS,GAAG,EAAE,CAAA;gBAClB,MAAM,UAAU,GAAkB,EAAE,CAAA;gBACpC,IAAI,QAAQ,CAAC,sBAAsB,EAAE,CAAC;oBAClC,SAAS,GAAG,UAAU;yBACjB,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;wBACnB,OAAO,QAAQ,CAAC,cAAc;6BACzB,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE;4BACnB,MAAM,QAAQ,GAAG,oBAAoB,KAAK,IAAI,aAAa,CAAC,YAAY,EAAE,CAAA;4BAC1E,MAAM,cAAc,GAChB,yBAAW,CAAC,UAAU,CAClB,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,MAAM,GAAG,aAAa,EACtB,aAAa,CAAC,YAAY,CAC7B,CAAA;4BACL,UAAU,CAAC,QAAQ,CAAC;gCAChB,MAAM,CAAC,cAAc,CAAC,CAAA;4BAC1B,OAAO,GAAG,aAAa,IAAI,aAAa,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAA;wBACxE,CAAC,CAAC;6BACD,IAAI,CAAC,OAAO,CAAC,CAAA;oBACtB,CAAC,CAAC;yBACD,IAAI,CAAC,MAAM,CAAC,CAAA;gBACrB,CAAC;qBAAM,CAAC;oBACJ,MAAM,KAAK,GAAG,yBAAW,CAAC,UAAU,CAChC,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,MAAM,GAAG,aAAa,EACtB,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,YAAY,CAC1C,CAAA;oBAED,MAAM,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;oBACrD,MAAM,aAAa,GAAG,GAAG,CAAC,KAAK,CAC3B,CAAC,EAAO,EAAE,EAAE,CAAC,OAAO,EAAE,KAAK,QAAQ,CACtC,CAAA;oBACD,IAAI,aAAa,EAAE,CAAC;wBAChB,8EAA8E;wBAC9E,SAAS,GAAG,GAAG,aAAa,IACxB,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,YAC/B,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAA;oBAC7B,CAAC;yBAAM,CAAC;wBACJ,UAAU,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAA;wBACpC,SAAS;4BACL,aAAa;gCACb,GAAG;gCACH,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,YAAY;gCACvC,4BAA4B,CAAA;oBACpC,CAAC;gBACL,CAAC;gBACD,UAAU,GAAG,MAAM,IAAI,CAAC,KAAK,EAAE;qBAC1B,kBAAkB,CAAC;oBAChB,8BAA8B,EAAE,SAAS;iBAC5C,CAAC;qBACD,aAAa,CAAC,UAAU,CAAC;qBACzB,cAAc,CAAC,WAAW,CAAC,CAAA;YACpC,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QACvD,CAAC;QAED,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,sCAAsC;YACtC,MAAM,oBAAoB,GAAG,MAAM,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YACpE,MAAM,uBAAuB,GAAG,MAAM,mBAAmB,CAAC,IAAI,CAC1D,UAAU,CACb,CAAA;YACD,MAAM,WAAW,GAAG,IAAI,mEAAgC,CACpD,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,oBAAoB,EACpB,uBAAuB,EACvB,IAAI,CAAC,WAAW,CACnB,CAAA;YACD,QAAQ,GAAG,WAAW,CAAC,SAAS,CAC5B,UAAU,EACV,IAAI,CAAC,aAAa,CAAC,SAAU,CAChC,CAAA;YAED,oCAAoC;YACpC,IACI,IAAI,CAAC,aAAa,CAAC,aAAa,KAAK,IAAI;gBACzC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,EAC1C,CAAC;gBACC,MAAM,WAAW,CAAC,WAAW,CAAC,SAAS,CACnC,MAAM,EACN,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,EACrC,QAAQ,CACX,CAAA;YACL,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,CAAC,oBAAoB,KAAK,OAAO,EAAE,CAAC;YACtD,MAAM,6BAA6B,GAC/B,IAAI,mCAA6B,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;YAEnE,MAAM,OAAO,CAAC,GAAG,CACb,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;gBAC1C,MAAM,cAAc,GAAG,QAAQ,CAAC,qBAAqB,CAAC,MAAM,CAAA;gBAC5D,MAAM,aAAa,GACf,QAAQ,CAAC,qBAAqB,CAAC,UAAU,CAAA;gBAE7C,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;oBACjD,CAAC,CAAC,mBAAQ,CAAC,2BAA2B,CAChC,IAAI,CAAC,WAAW,CAAC,MAAkB,CACtC;oBACH,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAA;gBAC7B,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;oBACvD,CAAC,CAAC,mBAAQ,CAAC,2BAA2B,CAChC,IAAI,CAAC,WAAW,CAAC,SAAS,CAC7B;oBACH,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAA;gBAEhC,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC;qBACpD,MAAM,CAAC,aAAa,CAAC;qBACrB,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC;qBACnC,cAAc,CAAC;oBACZ,MAAM,EAAE,MAAM;wBACV,CAAC,CAAC,mBAAQ,CAAC,SAAS,CACd,MAAM,EACN,QAAQ,CAAC,YAAY,CACxB;wBACH,CAAC,CAAC,SAAS;oBACf,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK;wBACzB,CAAC,CAAC,mBAAQ,CAAC,SAAS,CACd,IAAI,CAAC,WAAW,CAAC,KAAK,EACtB,QAAQ,CAAC,YAAY,CACxB;wBACH,CAAC,CAAC,SAAS;oBACf,SAAS,EAAE,SAAS;wBAChB,CAAC,CAAC,mBAAQ,CAAC,SAAS,CACd,SAAS,EACT,QAAQ,CAAC,YAAY,CACxB;wBACH,CAAC,CAAC,SAAS;oBACf,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,WAAW;oBACzC,oBAAoB,EAChB,IAAI,CAAC,WAAW,CAAC,oBAAoB;iBAC5C,CAAC,CAAA;gBACN,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACtB,MAAM,mBAAmB,GACrB,MAAM,6BAA6B,CAAC,iCAAiC,CACjE,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,YAAY,CACf,CAAA;oBACL,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;wBACxB,MAAM,kBAAkB,GAAG,mBAAmB,CAAC,IAAI,CAC/C,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,CACrC,CAAA;wBACD,IAAI,kBAAkB,EAAE,CAAC;4BACrB,MAAM,KAAK,GACP,kBAAkB,CAAC,OAAO,KAAK,SAAS;gCACpC,CAAC,CAAC,IAAI;gCACN,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAA;4BACpC,QAAQ,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;wBAC1C,CAAC;oBACL,CAAC,CAAC,CAAA;gBACN,CAAC;YACL,CAAC,CAAC,CACL,CAAA;QACL,CAAC;QAED,OAAO;YACH,GAAG,EAAE,UAAU;YACf,QAAQ,EAAE,QAAQ;SACrB,CAAA;IACL,CAAC;IAES,yCAAyC,CAC/C,WAAmB;QAEnB,6CAA6C;QAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAA;QAC/C,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;aACrC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE;YACnB,IAAI,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACpC,MAAM,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;gBAC9C,MAAM,SAAS,GAAG,aAAa,CAAC,CAAC,CAAC,CAAA;gBAClC,MAAM,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBACrD,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,SAAS,CAAC,CAAA;gBAC3D,MAAM,MAAM,GACR,KAAK,CAAC,QAAQ,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAA;gBAC3D,OAAO,CACH,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;oBACxB,GAAG;oBACH,IAAI,CAAC,MAAM,CACP,yBAAW,CAAC,UAAU,CAClB,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,SAAS,EACT,MAAO,CAAC,YAAY,CACvB,CACJ,CACJ,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,IACI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAC3B,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,SAAS,KAAK,aAAa;oBAClC,MAAM,CAAC,SAAS,KAAK,aAAa,CACzC;oBAED,OAAO,CACH,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;wBACxB,GAAG;wBACH,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAC7B,CAAA;gBAEL,OAAO,EAAE,CAAA;YACb,CAAC;QACL,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,CAAA;QAEf,MAAM,aAAa,GAAqB,EAAE,CAAA;QAC1C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;YAC5C,IAAI,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACpC,MAAM,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;gBAC9C,MAAM,SAAS,GAAG,aAAa,CAAC,CAAC,CAAC,CAAA;gBAClC,MAAM,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBACrD,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,SAAS,CAAC,CAAA;gBAC3D,MAAM,MAAM,GACR,KAAK,CAAC,QAAQ,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAA;gBAC3D,aAAa,CACT,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;oBACpB,GAAG;oBACH,IAAI,CAAC,MAAM,CACP,yBAAW,CAAC,UAAU,CAClB,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,SAAS,EACT,MAAO,CAAC,YAAY,CACvB,CACJ,CACR,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAA;YAC/B,CAAC;iBAAM,CAAC;gBACJ,IACI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAC3B,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,SAAS,KAAK,aAAa;oBAClC,MAAM,CAAC,SAAS,KAAK,aAAa,CACzC,EACH,CAAC;oBACC,aAAa,CACT,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;wBACpB,GAAG;wBACH,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CACjC,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAA;gBAC/B,CAAC;qBAAM,CAAC;oBACJ,aAAa,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAA;gBAC1D,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAA;QAEF,OAAO,CAAC,YAAY,EAAE,aAAa,CAAC,CAAA;IACxC,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,cAAc,CAAC,WAAwB;QACnD,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAA;QACtD,MAAM,OAAO,GACT,GAAG;YACH,kBAAkB;YAClB,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CACpC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,KAAK,CACvD,CAAA;QACL,MAAM,YAAY,GACd,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,KAAK,QAAQ;YAC7C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK;YAC/B,CAAC,CAAC,EAAE,CAAA;QACZ,IAAI,4BAA4B,GAC5B,SAAS,CAAA;QACb,MAAM,gBAAgB;QAClB,0DAA0D;QAC1D,CAAC,YAAY,CAAC,aAAa;YACvB,IAAI,CAAC,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;YACvC,yCAAyC;YACzC,IAAI,CAAC,aAAa,CAAC,KAAK,KAAK,IAAI,CAAA;QACrC,IAAI,UAAU,GAAG,KAAK,CAAA;QACtB,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,EAAE,CAAC;YACvD,IAAI,CAAC;gBACD,4BAA4B;oBACxB,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,YAAY,CAC/C;wBACI,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO;wBACtC,KAAK,EAAE,OAAO;wBACd,QAAQ,EACJ,IAAI,CAAC,aAAa,CAAC,aAAa;4BAChC,YAAY,CAAC,QAAQ;4BACrB,IAAI;qBACX,EACD,WAAW,CACd,CAAA;gBACL,IACI,4BAA4B;oBAC5B,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,CACvC,4BAA4B,CAC/B,EACH,CAAC;oBACC,OAAO,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,MAAM,CAAC,CAAA;gBAC1D,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;oBAC7B,MAAM,KAAK,CAAA;gBACf,CAAC;gBACD,UAAU,GAAG,IAAI,CAAA;YACrB,CAAC;QACL,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAE9D,IACI,CAAC,UAAU;YACX,IAAI,CAAC,UAAU,CAAC,gBAAgB;YAChC,gBAAgB,EAClB,CAAC;YACC,IAAI,CAAC;gBACD,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,YAAY,CAC/C;oBACI,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO;oBACtC,KAAK,EAAE,OAAO;oBACd,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE;oBAChB,QAAQ,EACJ,IAAI,CAAC,aAAa,CAAC,aAAa;wBAChC,YAAY,CAAC,QAAQ;wBACrB,IAAI;oBACR,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC;iBAC1C,EACD,4BAA4B,EAC5B,WAAW,CACd,CAAA;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;oBAC7B,MAAM,KAAK,CAAA;gBACf,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC,OAAO,CAAA;IAC1B,CAAC;IAED;;OAEG;IACO,kBAAkB,CACxB,aAA0C;QAE1C,yBAAW,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,CAAA;QACrD,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACO,eAAe,CAAC,GAAQ;QAC9B,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,SAAS,IAAI,GAAG,KAAK,IAAI;YAC5D,OAAO,GAAG,CAAA;QAEd,OAAO,MAAM,CAAC,GAAG,CAAC,CAAA;IACtB,CAAC;IAED;;OAEG;IACO,iBAAiB;QACvB,OAAO,CACH,IAAI,CAAC,WAAW;YAChB,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAC7B,IAAI,CAAC,UAAU,CAAC,8BAA8B,EAAE,CACnD,CACJ,CAAA;IACL,CAAC;IAES,WAAW,CACjB,MAA8B,EAC9B,QAAwB,EACxB,KAAa,EACb,WAAoB;QAEpB,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACvB,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,SAAS,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,KAAK;gBAAE,SAAQ;YAEhE,MAAM,YAAY,GAAG,WAAW,CAAC,CAAC,CAAC,WAAW,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;YAChE,MAAM,MAAM,GACR,QAAQ,CAAC,gCAAgC,CAAC,YAAY,CAAC,CAAA;YAC3D,MAAM,KAAK,GAAG,QAAQ,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAA;YACjE,MAAM,QAAQ,GAAG,QAAQ,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAA;YAEpE,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ;gBAC9B,MAAM,IAAI,yDAA2B,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAA;YAEjE,IAAI,MAAM,EAAE,CAAC;gBACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,YAAY,CAAC,CAAA;gBAC7C,8CAA8C;YAClD,CAAC;iBAAM,IAAI,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,WAAW,CACZ,MAAM,CAAC,GAAG,CAA2B,EACrC,QAAQ,EACR,KAAK,EACL,YAAY,CACf,CAAA;gBAED,yBAAyB;gBACzB,6DAA6D;gBAC7D,2EAA2E;gBAC3E,wBAAwB;gBACxB,4BAA4B;gBAC5B,4BAA4B;gBAC5B,6BAA6B;gBAC7B,gCAAgC;gBAChC,kCAAkC;gBAClC,yCAAyC;gBACzC,cAAc;gBACd,QAAQ;gBACR,wGAAwG;YAC5G,CAAC;QACL,CAAC;IACL,CAAC;IAES,cAAc,CACpB,SAAoC,EACpC,SAA6C,EAC7C,QAAwB,EACxB,KAAa,EACb,WAAoB;QAEpB,IAAI,CAAC,SAAS;YAAE,OAAM;QAEtB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;YAC5C,MAAM,aAAa,GAAI,SAAiB,CAAC,YAAY,CAAC,CAAA;YACtD,MAAM,YAAY,GAAG,WAAW;gBAC5B,CAAC,CAAC,WAAW,GAAG,GAAG,GAAG,YAAY;gBAClC,CAAC,CAAC,YAAY,CAAA;YAClB,MAAM,KAAK,GAAG,QAAQ,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAA;YACjE,MAAM,QAAQ,GAAG,QAAQ,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAA;YACpE,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ;gBACnB,MAAM,IAAI,yDAA2B,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAA;YAEjE,IAAI,KAAK,EAAE,CAAC;gBACR,IAAI,CAAC,cAAc,CACf,aAAa,EACb,OAAO,SAAS,KAAK,QAAQ;oBACzB,CAAC,CAAC,mBAAQ,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,YAAY,CAAC;oBACnD,CAAC,CAAC,SAAS,EACf,QAAQ,EACR,KAAK,EACL,YAAY,CACf,CAAA;YACL,CAAC;iBAAM,IAAI,QAAQ,EAAE,CAAC;gBAClB,IAAI,SAAS,GAAG,KAAK,GAAG,GAAG,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;gBAC5D,SAAS,GAAG,yBAAW,CAAC,UAAU,CAC9B,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,EAAE,MAAM,EAAE,IAAI,EAAE,EAChB,KAAK,EACL,SAAS,CACZ,CAAA;gBACD,IACI,aAAa,KAAK,IAAI;oBACtB,OAAO,aAAa,KAAK,QAAQ,EACnC,CAAC;oBACC,IAAI,IAAI,CAAC,aAAa,CAAC,oBAAoB,KAAK,OAAO,EAAE,CAAC;wBACtD,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAA;oBACzC,CAAC;yBAAM,CAAC;wBACJ,OAAO;wBACP,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;4BACZ,IAAI,EAAE,MAAM;4BACZ,MAAM,EAAE,IAAI;4BACZ,SAAS,EACL,SAAS;gCACT,OAAO,SAAS,CAAC,YAAY,CAAC,KAAK,QAAQ;gCACvC,CAAC,CAAE,SAAS,CACN,YAAY,CACY;gCAC9B,CAAC,CAAC,SAAS;4BACnB,KAAK,EAAE,SAAS;4BAChB,WAAW,EAAE,KAAK;4BAClB,gBAAgB,EAAE,QAAQ;yBAC7B,CAAC,CAAA;wBAEF,IACI,SAAS;4BACT,OAAO,SAAS,CAAC,YAAY,CAAC,KAAK,QAAQ,EAC7C,CAAC;4BACC,IAAI,CAAC,WAAW,CACZ,SAAS,CACL,YAAY,CACW,EAC3B,QAAQ,CAAC,qBAAqB,EAC9B,SAAS,CACZ,CAAA;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,IACI,OAAO,aAAa,KAAK,QAAQ;oBACjC,IAAI,CAAC,aAAa,CAAC,oBAAoB,KAAK,MAAM,EACpD,CAAC;oBACC,IAAI,CAAC,cAAc,CACf,aAAa,EACb,OAAO,SAAS,KAAK,QAAQ;wBACzB,CAAC,CAAC,mBAAQ,CAAC,SAAS,CACd,SAAS,EACT,QAAQ,CAAC,YAAY,CACxB;wBACH,CAAC,CAAC,SAAS,EACf,QAAQ,CAAC,qBAAqB,EAC9B,SAAS,EACT,SAAS,CACZ,CAAA;gBACL,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAES,mBAAmB,CACzB,SAAoC,EACpC,SAA6C,EAC7C,QAAwB,EACxB,KAAa,EACb,WAAoB;QAEpB,IAAI,CAAC,SAAS;YAAE,OAAM;QAEtB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;YAC5C,MAAM,aAAa,GAAI,SAAiB,CAAC,YAAY,CAAC,CAAA;YACtD,MAAM,YAAY,GAAG,WAAW;gBAC5B,CAAC,CAAC,WAAW,GAAG,GAAG,GAAG,YAAY;gBAClC,CAAC,CAAC,YAAY,CAAA;YAClB,MAAM,KAAK,GAAG,QAAQ,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAA;YACjE,MAAM,QAAQ,GAAG,QAAQ,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAA;YACpE,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ;gBACnB,MAAM,IAAI,yDAA2B,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAA;YAEjE,IAAI,KAAK,EAAE,CAAC;gBACR,IAAI,CAAC,mBAAmB,CACpB,aAAa,EACb,OAAO,SAAS,KAAK,QAAQ;oBACzB,CAAC,CAAC,mBAAQ,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,YAAY,CAAC;oBACnD,CAAC,CAAC,SAAS,EACf,QAAQ,EACR,KAAK,EACL,YAAY,CACf,CAAA;YACL,CAAC;iBAAM,IAAI,QAAQ,EAAE,CAAC;gBAClB,IAAI,SAAS,GAAG,KAAK,GAAG,GAAG,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;gBAC5D,SAAS,GAAG,yBAAW,CAAC,UAAU,CAC9B,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,EAAE,MAAM,EAAE,IAAI,EAAE,EAChB,KAAK,EACL,SAAS,CACZ,CAAA;gBAED,IACI,aAAa,KAAK,IAAI;oBACtB,OAAO,aAAa,KAAK,QAAQ,EACnC,CAAC;oBACC,QAAQ,CAAC,qBAAqB,CAAC,cAAc,CAAC,OAAO,CACjD,CAAC,aAAa,EAAE,EAAE;wBACd,IAAI,sBAAsB,GACtB,SAAS;4BACT,GAAG;4BACH,aAAa,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;wBAChD,sBAAsB,GAAG,yBAAW,CAAC,UAAU,CAC3C,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,EAAE,MAAM,EAAE,IAAI,EAAE,EAChB,SAAS,EACT,sBAAsB,CACzB,CAAA;wBAED,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAC7B,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,KAAK,sBAAsB,CAClD,CAAA;wBACD,IAAI,CAAC,SAAS,EAAE,CAAC;4BACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gCACZ,IAAI,EAAE,MAAM;gCACZ,MAAM,EAAE,IAAI;gCACZ,KAAK,EAAE,sBAAsB;gCAC7B,WAAW,EAAE,SAAS;gCACtB,SAAS,EAAE,SAAS;gCACpB,gBAAgB,EAAE,aAAa;6BAClC,CAAC,CAAA;wBACN,CAAC;wBAED,IACI,SAAS;4BACT,OAAO,SAAS,CAAC,YAAY,CAAC,KAAK,QAAQ,EAC7C,CAAC;4BACC,IAAI,CAAC,WAAW,CACZ,SAAS,CACL,YAAY,CACW,EAC3B,QAAQ,CAAC,qBAAqB,EAC9B,SAAS,CACZ,CAAA;wBACL,CAAC;oBACL,CAAC,CACJ,CAAA;gBACL,CAAC;gBAED,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE,CAAC;oBACpC,IAAI,CAAC,mBAAmB,CACpB,aAAa,EACb,OAAO,SAAS,KAAK,QAAQ;wBACzB,CAAC,CAAC,mBAAQ,CAAC,SAAS,CACd,SAAS,EACT,QAAQ,CAAC,YAAY,CACxB;wBACH,CAAC,CAAC,SAAS,EACf,QAAQ,CAAC,qBAAqB,EAC9B,SAAS,EACT,SAAS,CACZ,CAAA;gBACL,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAES,UAAU,CAChB,KAA4B,EAC5B,QAAwB,EACxB,KAAa,EACb,WAAoB;QAEpB,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;YACtB,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,SAAS;gBAAE,SAAQ;YAEtC,MAAM,YAAY,GAAG,WAAW,CAAC,CAAC,CAAC,WAAW,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;YAChE,MAAM,MAAM,GACR,QAAQ,CAAC,gCAAgC,CAAC,YAAY,CAAC,CAAA;YAC3D,MAAM,KAAK,GAAG,QAAQ,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAA;YACjE,MAAM,QAAQ,GAAG,QAAQ,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAA;YAEpE,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ;gBAC9B,MAAM,IAAI,yDAA2B,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAA;YAEjE,IAAI,MAAM,EAAE,CAAC;gBACT,IAAI,SAAS,GACT,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,QAAQ;oBAC1B,CAAC,CAAE,KAAK,CAAC,GAAG,CAAS,CAAC,SAAS;oBAC/B,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;gBACpB,SAAS;oBACL,SAAS,KAAK,MAAM;wBACpB,SAAS,KAAK,MAAM;wBACpB,SAAS,KAAK,CAAC,CAAC;wBACZ,CAAC,CAAC,MAAM;wBACR,CAAC,CAAC,KAAK,CAAA;gBACf,IAAI,KAAK,GACL,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,QAAQ;oBAC1B,CAAC,CAAE,KAAK,CAAC,GAAG,CAAS,CAAC,KAAK;oBAC3B,CAAC,CAAC,SAAS,CAAA;gBACnB,KAAK;oBACD,KAAK,EAAE,WAAW,EAAE,KAAK,OAAO;wBAC5B,CAAC,CAAC,aAAa;wBACf,CAAC,CAAC,KAAK,EAAE,WAAW,EAAE,KAAK,MAAM;4BACjC,CAAC,CAAC,YAAY;4BACd,CAAC,CAAC,SAAS,CAAA;gBAEnB,MAAM,SAAS,GAAG,GAAG,KAAK,IAAI,YAAY,EAAE,CAAA;gBAC5C,qDAAqD;gBACrD,wCAAwC;gBACxC,IAAI;gBACJ,mBAAmB;gBACnB,+CAA+C;gBAC/C,+BAA+B;gBAC/B,kCAAkC;gBAClC,sCAAsC;gBACtC,yBAAyB;gBACzB,qBAAqB;gBACrB,mCAAmC;gBACnC,aAAa;gBACb,QAAQ;gBACR,yCAAyC;gBACzC,WAAW;gBACX,sDAAsD;gBACtD,iDAAiD;gBACjD,QAAQ;gBACR,IAAI;gBAEJ,kEAAkE;gBAClE,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAA;gBAC5C,+EAA+E;YACnF,CAAC;iBAAM,IAAI,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,UAAU,CACX,KAAK,CAAC,GAAG,CAA0B,EACnC,QAAQ,EACR,KAAK,EACL,YAAY,CACf,CAAA;YACL,CAAC;iBAAM,IAAI,QAAQ,EAAE,CAAC;gBAClB,IAAI,SAAS,GAAG,KAAK,GAAG,GAAG,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;gBAC5D,SAAS,GAAG,yBAAW,CAAC,UAAU,CAC9B,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,EAAE,MAAM,EAAE,IAAI,EAAE,EAChB,KAAK,EACL,SAAS,CACZ,CAAA;gBACD,+FAA+F;gBAC/F,qEAAqE;gBACrE,iFAAiF;gBAEjF,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAC7B,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,KAAK,SAAS,CACrC,CAAA;gBACD,IAAI,CAAC,SAAS,EAAE,CAAC;oBACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,MAAM;wBACZ,MAAM,EAAE,KAAK;wBACb,KAAK,EAAE,SAAS;wBAChB,WAAW,EAAE,KAAK;wBAClB,SAAS,EAAE,SAAS;wBACpB,gBAAgB,EAAE,QAAQ;qBAC7B,CAAC,CAAA;gBACN,CAAC;gBACD,IAAI,CAAC,UAAU,CACX,KAAK,CAAC,GAAG,CAA0B,EACnC,QAAQ,CAAC,qBAAqB,EAC9B,SAAS,CACZ,CAAA;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAES,UAAU,CAChB,KAAsD,EACtD,QAAwB,EACxB,KAAa,EACb,WAAoB;QAEpB,IAAI,SAAS,GAAW,EAAE,CAAA;QAC1B,gFAAgF;QAChF,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACvB,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gBACf,SAAS,GAAG,KAAK;qBACZ,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;oBACf,OAAO,IAAI,CAAC,UAAU,CAClB,SAAS,EACT,QAAQ,EACR,KAAK,EACL,WAAW,CACd,CAAA;gBACL,CAAC,CAAC;qBACD,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;qBAClC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG,CAAC;qBACzC,IAAI,CAAC,MAAM,CAAC,CAAA;YACrB,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,aAAa,GAAa,EAAE,CAAA;YAClC,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;gBACtB,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,SAAS,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,IAAI;oBAAE,SAAQ;gBAE7D,MAAM,YAAY,GAAG,WAAW,CAAC,CAAC,CAAC,WAAW,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;gBAChE,MAAM,MAAM,GACR,QAAQ,CAAC,gCAAgC,CAAC,YAAY,CAAC,CAAA;gBAC3D,MAAM,KAAK,GACP,QAAQ,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAA;gBACvD,MAAM,QAAQ,GACV,QAAQ,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAA;gBAEvD,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ;oBAC9B,MAAM,IAAI,yDAA2B,CACjC,YAAY,EACZ,QAAQ,CACX,CAAA;gBAEL,IAAI,MAAM,EAAE,CAAC;oBACT,IAAI,SAAS,GAAG,GAAG,KAAK,IAAI,YAAY,EAAE,CAAA;oBAC1C,IAAI,MAAM,CAAC,iBAAiB,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;wBAC3C,SAAS,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAA;oBACvD,CAAC;oBACD,gGAAgG;oBAEhG,mDAAmD;oBACnD,IAAI,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,CAAA;oBAC/B,IAAI,iCAAe,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;wBAC9C,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAA;oBACrC,CAAC;oBAED,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;wBACrB,IAAI,cAAc,YAAY,2BAAY,EAAE,CAAC;4BACzC,cAAc,CAAC,cAAc,CAAC,MAAM,CAAC,WAAW,CAAC,CAAA;wBACrD,CAAC;6BAAM,CAAC;4BACJ,cAAc,GAAG,+CAAsB,CAAC,WAAW,CAC/C,MAAM,CAAC,WAAW,EAClB,cAAc,CACjB,CAAA;wBACL,CAAC;oBACL,CAAC;oBAED,4DAA4D;oBAC5D,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;wBAClD,cAAc,GACV,IAAI,CAAC,UAAU,CAAC,MACnB,CAAC,iBAAiB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAA;oBAC/C,CAAC;oBAED,iCAAiC;oBACjC,kDAAkD;oBAClD,EAAE;oBACF,uDAAuD;oBACvD,qCAAqC;oBACrC,4CAA4C;oBAC5C,mIAAmI;oBACnI,4FAA4F;oBAC5F,SAAS;oBACT,qHAAqH;oBACrH,+DAA+D;oBAC/D,0DAA0D;oBAC1D,SAAS;oBACT,0BAA0B;oBAC1B,wHAAwH;oBACxH,uCAAuC;oBACvC,2IAA2I;oBAC3I,mBAAmB;oBACnB,iBAAiB;oBACjB,WAAW;oBACX,0BAA0B;oBAC1B,0GAA0G;oBAC1G,4EAA4E;oBAC5E,QAAQ;oBACR,EAAE;oBACF,WAAW;oBACX,2EAA2E;oBAC3E,wBAAwB;oBACxB,mGAAmG;oBACnG,yDAAyD;oBACzD,IAAI;oBAEJ,aAAa,CAAC,IAAI,CACd,IAAI,CAAC,8BAA8B,CAC/B,IAAI,CAAC,0BAA0B,CAC3B,SAAS,EACT,cAAc,CACjB,CACJ,CAEJ,CAAA;oBAED,oEAAoE;oBACpE,wGAAwG;gBAC5G,CAAC;qBAAM,IAAI,KAAK,EAAE,CAAC;oBACf,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAC7B,KAAK,CAAC,GAAG,CAAC,EACV,QAAQ,EACR,KAAK,EACL,YAAY,CACf,CAAA;oBACD,IAAI,SAAS;wBAAE,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;gBAChD,CAAC;qBAAM,IAAI,QAAQ,EAAE,CAAC;oBAClB,0EAA0E;oBAC1E,wEAAwE;oBACxE,IAAI,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;wBACjC,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CACjD,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,CACrC,CAAA;wBACD,IAAI,eAAe,EAAE,CAAC;4BAClB,SAAQ;wBACZ,CAAC;oBACL,CAAC;oBAED,IAAI,iCAAe,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;wBAC7C,IACI,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,UAAU;4BAC9B,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,UAAU;4BAC9B,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,iBAAiB;4BACrC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,iBAAiB,EACvC,CAAC;4BACC,IAAI,WAAW,GAAG,EAAE,CAAA;4BACpB,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gCACjC,WAAW,GAAG,GAAG,CAAA;4BACrB,CAAC;iCAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gCACxC,WAAW,GAAG,GAAG,CAAA;4BACrB,CAAC;iCAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;gCAC/C,WAAW,GAAG,IAAI,CAAA;4BACtB,CAAC;iCAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;gCAC/C,WAAW,GAAG,IAAI,CAAA;4BACtB,CAAC;4BACD,yCAAyC;4BACzC,MAAM,EAAE,GAAsB,IAAI,CAAC,QAAQ,EAAE,CAAA;4BAC7C,IAAI,QAAQ,CAAC,iBAAiB,EAAE,CAAC;gCAC7B,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC;qCAChB,IAAI,CACD,QAAQ,CAAC,aAAa,EACtB,QAAQ,CAAC,aAAa,CACzB;qCACA,KAAK,CACF,QAAQ,CAAC,WAAW;qCACf,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;oCACZ,OAAO,GACH,QAAQ,CAAC,aACb,IACI,MAAM,CAAC,YACX,MAAM,KAAK,IACP,MAAM,CAAC,gBAAiB;yCACnB,YACT,EAAE,CAAA;gCACN,CAAC,CAAC;qCACD,IAAI,CAAC,OAAO,CAAC,CACrB,CAAA;4BACT,CAAC;iCAAM,IAAI,QAAQ,CAAC,oBAAoB,EAAE,CAAC;gCACvC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC;qCAChB,IAAI,CACD,QAAQ,CAAC,eAAgB,CAAC,aAAa,EACvC,QAAQ,CAAC,eAAgB,CAAC,aAAa,CAC1C;qCACA,KAAK,CACF,QAAQ;qCACH,eAAgB,CAAC,kBAAkB,CAAC,GAAG,CACpC,CAAC,MAAM,EAAE,EAAE;oCACP,OAAO,GACH,QAAQ,CAAC,eAAgB;yCACpB,aACT,IACI,MAAM,CAAC,YACX,MAAM,KAAK,IACP,MAAM,CAAC,gBAAiB;yCACnB,YACT,EAAE,CAAA;gCACN,CAAC,CACJ;qCACA,IAAI,CAAC,OAAO,CAAC,CACrB,CAAA;4BACT,CAAC;iCAAM,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;gCAC9B,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC;qCAChB,IAAI,CACD,QAAQ,CAAC,qBAAqB,CAAC,MAAM,EACrC,QAAQ,CAAC,qBAAqB;qCACzB,SAAS,CACjB;qCACA,KAAK,CACF,QAAQ;qCACH,eAAgB,CAAC,WAAW,CAAC,GAAG,CAC7B,CAAC,MAAM,EAAE,EAAE;oCACP,OAAO,GACH,QAAQ;yCACH,qBAAqB;yCACrB,SACT,IACI,MAAM,CAAC,YACX,MAAM,KAAK,IACP,MAAM,CAAC,gBAAiB;yCACnB,YACT,EAAE,CAAA;gCACN,CAAC,CACJ;qCACA,IAAI,CAAC,OAAO,CAAC,CACrB,CAAA;4BACT,CAAC;iCAAM,CAAC;gCACJ,MAAM,IAAI,KAAK,CACX,sDAAsD,CACzD,CAAA;4BACL,CAAC;4BACD,OAAO;4BACP,mEAAmE;4BACnE,0HAA0H;4BAC1H,IAAI,CAAC,QAAQ,CACT,EAAE,CAAC,MAAM,EAAE;gCACP,GAAG;gCACH,WAAW;gCACX,GAAG;gCACH,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CACjC,CAAA;wBACL,CAAC;6BAAM,CAAC;4BACJ,IACI,QAAQ,CAAC,WAAW;gCACpB,CAAC,QAAQ,CAAC,UAAU;oCAChB,QAAQ,CAAC,eAAe,CAAC,EAC/B,CAAC;gCACC,MAAM,SAAS,GAAG,GAAG,KAAK,IAAI,YAAY,EAAE,CAAA;gCAE5C,aAAa,CAAC,IAAI,CACd,IAAI,CAAC,8BAA8B,CAC/B,IAAI,CAAC,0BAA0B,CAC3B,SAAS,EACT,KAAK,CAAC,GAAG,CAAC,CACb,CACJ,CACJ,CAAA;4BACL,CAAC;iCAAM,CAAC;gCACJ,MAAM,IAAI,KAAK,CACX,sDAAsD,CACzD,CAAA;4BACL,CAAC;wBACL,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACJ,yDAAyD;wBACzD,IAAI,SAAS,GACT,KAAK;4BACL,GAAG;4BACH,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;wBAC3C,SAAS,GAAG,yBAAW,CAAC,UAAU,CAC9B,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,EAAE,MAAM,EAAE,IAAI,EAAE,EAChB,KAAK,EACL,SAAS,CACZ,CAAA;wBAED,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAC7B,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,KAAK,SAAS,CACrC,CAAA;wBACD,IAAI,CAAC,SAAS,EAAE,CAAC;4BACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gCACZ,IAAI,EAAE,MAAM;gCACZ,MAAM,EAAE,KAAK;gCACb,SAAS,EAAE,SAAS;gCACpB,KAAK,EAAE,SAAS;gCAChB,WAAW,EAAE,KAAK;gCAClB,gBAAgB,EAAE,QAAQ;6BAC7B,CAAC,CAAA;wBACN,CAAC;wBAED,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAC7B,KAAK,CAAC,GAAG,CAAC,EACV,QAAQ,CAAC,qBAAqB,EAC9B,SAAS,CACZ,CAAA;wBACD,IAAI,SAAS,EAAE,CAAC;4BACZ,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;4BAC7B,4EAA4E;wBAChF,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YACD,SAAS,GAAG,aAAa,CAAC,MAAM;gBAC5B,CAAC,CAAC,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG;gBAC3C,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACrC,CAAC;QACD,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,SAAS,CAAA;IAC/D,CAAC;CACJ;AAp3ID,gDAo3IC", "file": "SelectQueryBuilder.js", "sourcesContent": ["import { RawSqlResultsToEntityTransformer } from \"./transformer/RawSqlResultsToEntityTransformer\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { PessimisticLockTransactionRequiredError } from \"../error/PessimisticLockTransactionRequiredError\"\nimport { NoVersionOrUpdateDateColumnError } from \"../error/NoVersionOrUpdateDateColumnError\"\nimport { OptimisticLockVersionMismatchError } from \"../error/OptimisticLockVersionMismatchError\"\nimport { OptimisticLockCanNotBeUsedError } from \"../error/OptimisticLockCanNotBeUsedError\"\nimport { JoinAttribute } from \"./JoinAttribute\"\nimport { RelationIdAttribute } from \"./relation-id/RelationIdAttribute\"\nimport { RelationCountAttribute } from \"./relation-count/RelationCountAttribute\"\nimport { RelationIdLoader } from \"./relation-id/RelationIdLoader\"\nimport { RelationIdLoader as QueryStrategyRelationIdLoader } from \"./RelationIdLoader\"\nimport { RelationIdMetadataToAttributeTransformer } from \"./relation-id/RelationIdMetadataToAttributeTransformer\"\nimport { RelationCountLoader } from \"./relation-count/RelationCountLoader\"\nimport { RelationCountMetadataToAttributeTransformer } from \"./relation-count/RelationCountMetadataToAttributeTransformer\"\nimport { QueryBuilder } from \"./QueryBuilder\"\nimport { ReadStream } from \"../platform/PlatformTools\"\nimport { LockNotSupportedOnGivenDriverError } from \"../error/LockNotSupportedOnGivenDriverError\"\nimport { MysqlDriver } from \"../driver/mysql/MysqlDriver\"\nimport { SelectQuery } from \"./SelectQuery\"\nimport { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport { OrderByCondition } from \"../find-options/OrderByCondition\"\nimport { QueryExpressionMap } from \"./QueryExpressionMap\"\nimport { EntityTarget } from \"../common/EntityTarget\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { WhereExpressionBuilder } from \"./WhereExpressionBuilder\"\nimport { Brackets } from \"./Brackets\"\nimport { QueryResultCacheOptions } from \"../cache/QueryResultCacheOptions\"\nimport { OffsetWithoutLimitNotSupportedError } from \"../error/OffsetWithoutLimitNotSupportedError\"\nimport { SelectQueryBuilderOption } from \"./SelectQueryBuilderOption\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { DriverUtils } from \"../driver/DriverUtils\"\nimport { EntityNotFoundError } from \"../error/EntityNotFoundError\"\nimport { TypeORMError } from \"../error\"\nimport { FindManyOptions } from \"../find-options/FindManyOptions\"\nimport { FindOptionsSelect } from \"../find-options/FindOptionsSelect\"\nimport { RelationMetadata } from \"../metadata/RelationMetadata\"\nimport { FindOptionsOrder } from \"../find-options/FindOptionsOrder\"\nimport { FindOptionsWhere } from \"../find-options/FindOptionsWhere\"\nimport { FindOptionsUtils } from \"../find-options/FindOptionsUtils\"\nimport { FindOptionsRelations } from \"../find-options/FindOptionsRelations\"\nimport { OrmUtils } from \"../util/OrmUtils\"\nimport { EntityPropertyNotFoundError } from \"../error/EntityPropertyNotFoundError\"\nimport { AuroraMysqlDriver } from \"../driver/aurora-mysql/AuroraMysqlDriver\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\nimport { FindOperator } from \"../find-options/FindOperator\"\nimport { ApplyValueTransformers } from \"../util/ApplyValueTransformers\"\nimport { SqlServerDriver } from \"../driver/sqlserver/SqlServerDriver\"\n\n/**\n * Allows to build complex sql queries in a fashion way and execute those queries.\n */\nexport class SelectQueryBuilder<Entity extends ObjectLiteral>\n    extends QueryBuilder<Entity>\n    implements WhereExpressionBuilder\n{\n    readonly \"@instanceof\" = Symbol.for(\"SelectQueryBuilder\")\n\n    protected findOptions: FindManyOptions = {}\n    protected selects: string[] = []\n    protected joins: {\n        type: \"inner\" | \"left\"\n        alias: string\n        parentAlias: string\n        relationMetadata: RelationMetadata\n        select: boolean\n        selection: FindOptionsSelect<any> | undefined\n    }[] = []\n    protected conditions: string = \"\"\n    protected orderBys: {\n        alias: string\n        direction: \"ASC\" | \"DESC\"\n        nulls?: \"NULLS FIRST\" | \"NULLS LAST\"\n    }[] = []\n    protected relationMetadatas: RelationMetadata[] = []\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Gets generated SQL query without parameters being replaced.\n     */\n    getQuery(): string {\n        let sql = this.createComment()\n        sql += this.createCteExpression()\n        sql += this.createSelectExpression()\n        sql += this.createJoinExpression()\n        sql += this.createWhereExpression()\n        sql += this.createGroupByExpression()\n        sql += this.createHavingExpression()\n        sql += this.createOrderByExpression()\n        sql += this.createLimitOffsetExpression()\n        sql += this.createLockExpression()\n        sql = sql.trim()\n        if (this.expressionMap.subQuery) sql = \"(\" + sql + \")\"\n        return this.replacePropertyNamesForTheWholeQuery(sql)\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    setFindOptions(findOptions: FindManyOptions<Entity>) {\n        this.findOptions = findOptions\n        this.applyFindOptions()\n        return this\n    }\n\n    /**\n     * Creates a subquery - query that can be used inside other queries.\n     */\n    subQuery(): SelectQueryBuilder<any> {\n        const qb = this.createQueryBuilder()\n        qb.expressionMap.subQuery = true\n        qb.parentQueryBuilder = this\n        return qb\n    }\n\n    /**\n     * Creates SELECT query.\n     * Replaces all previous selections if they exist.\n     */\n    select(): this\n\n    /**\n     * Creates SELECT query.\n     * Replaces all previous selections if they exist.\n     */\n    select(\n        selection: (qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>,\n        selectionAliasName?: string,\n    ): this\n\n    /**\n     * Creates SELECT query and selects given data.\n     * Replaces all previous selections if they exist.\n     */\n    select(selection: string, selectionAliasName?: string): this\n\n    /**\n     * Creates SELECT query and selects given data.\n     * Replaces all previous selections if they exist.\n     */\n    select(selection: string[]): this\n\n    /**\n     * Creates SELECT query and selects given data.\n     * Replaces all previous selections if they exist.\n     */\n    select(\n        selection?:\n            | string\n            | string[]\n            | ((qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>),\n        selectionAliasName?: string,\n    ): SelectQueryBuilder<Entity> {\n        this.expressionMap.queryType = \"select\"\n        if (Array.isArray(selection)) {\n            this.expressionMap.selects = selection.map((selection) => ({\n                selection: selection,\n            }))\n        } else if (typeof selection === \"function\") {\n            const subQueryBuilder = selection(this.subQuery())\n            this.setParameters(subQueryBuilder.getParameters())\n            this.expressionMap.selects.push({\n                selection: subQueryBuilder.getQuery(),\n                aliasName: selectionAliasName,\n            })\n        } else if (selection) {\n            this.expressionMap.selects = [\n                { selection: selection, aliasName: selectionAliasName },\n            ]\n        }\n\n        return this\n    }\n\n    /**\n     * Adds new selection to the SELECT query.\n     */\n    addSelect(\n        selection: (qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>,\n        selectionAliasName?: string,\n    ): this\n\n    /**\n     * Adds new selection to the SELECT query.\n     */\n    addSelect(selection: string, selectionAliasName?: string): this\n\n    /**\n     * Adds new selection to the SELECT query.\n     */\n    addSelect(selection: string[]): this\n\n    /**\n     * Adds new selection to the SELECT query.\n     */\n    addSelect(\n        selection:\n            | string\n            | string[]\n            | ((qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>),\n        selectionAliasName?: string,\n    ): this {\n        if (!selection) return this\n\n        if (Array.isArray(selection)) {\n            this.expressionMap.selects = this.expressionMap.selects.concat(\n                selection.map((selection) => ({ selection: selection })),\n            )\n        } else if (typeof selection === \"function\") {\n            const subQueryBuilder = selection(this.subQuery())\n            this.setParameters(subQueryBuilder.getParameters())\n            this.expressionMap.selects.push({\n                selection: subQueryBuilder.getQuery(),\n                aliasName: selectionAliasName,\n            })\n        } else if (selection) {\n            this.expressionMap.selects.push({\n                selection: selection,\n                aliasName: selectionAliasName,\n            })\n        }\n\n        return this\n    }\n\n    /**\n     * Set max execution time.\n     * @param milliseconds\n     */\n    maxExecutionTime(milliseconds: number): this {\n        this.expressionMap.maxExecutionTime = milliseconds\n        return this\n    }\n\n    /**\n     * Sets whether the selection is DISTINCT.\n     */\n    distinct(distinct: boolean = true): this {\n        this.expressionMap.selectDistinct = distinct\n        return this\n    }\n\n    /**\n     * Sets the distinct on clause for Postgres.\n     */\n    distinctOn(distinctOn: string[]): this {\n        this.expressionMap.selectDistinctOn = distinctOn\n        return this\n    }\n\n    fromDummy(): SelectQueryBuilder<any> {\n        return this.from(\n            this.connection.driver.dummyTableName ??\n                \"(SELECT 1 AS dummy_column)\",\n            \"dummy_table\",\n        )\n    }\n\n    /**\n     * Specifies FROM which entity's table select/update/delete will be executed.\n     * Also sets a main string alias of the selection data.\n     * Removes all previously set from-s.\n     */\n    from<T extends ObjectLiteral>(\n        entityTarget: (qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>,\n        aliasName: string,\n    ): SelectQueryBuilder<T>\n\n    /**\n     * Specifies FROM which entity's table select/update/delete will be executed.\n     * Also sets a main string alias of the selection data.\n     * Removes all previously set from-s.\n     */\n    from<T extends ObjectLiteral>(\n        entityTarget: EntityTarget<T>,\n        aliasName: string,\n    ): SelectQueryBuilder<T>\n\n    /**\n     * Specifies FROM which entity's table select/update/delete will be executed.\n     * Also sets a main string alias of the selection data.\n     * Removes all previously set from-s.\n     */\n    from<T extends ObjectLiteral>(\n        entityTarget:\n            | EntityTarget<T>\n            | ((qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>),\n        aliasName: string,\n    ): SelectQueryBuilder<T> {\n        const mainAlias = this.createFromAlias(entityTarget, aliasName)\n        this.expressionMap.setMainAlias(mainAlias)\n        return this as any as SelectQueryBuilder<T>\n    }\n\n    /**\n     * Specifies FROM which entity's table select/update/delete will be executed.\n     * Also sets a main string alias of the selection data.\n     */\n    addFrom<T extends ObjectLiteral>(\n        entityTarget: (qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>,\n        aliasName: string,\n    ): SelectQueryBuilder<T>\n\n    /**\n     * Specifies FROM which entity's table select/update/delete will be executed.\n     * Also sets a main string alias of the selection data.\n     */\n    addFrom<T extends ObjectLiteral>(\n        entityTarget: EntityTarget<T>,\n        aliasName: string,\n    ): SelectQueryBuilder<T>\n\n    /**\n     * Specifies FROM which entity's table select/update/delete will be executed.\n     * Also sets a main string alias of the selection data.\n     */\n    addFrom<T extends ObjectLiteral>(\n        entityTarget:\n            | EntityTarget<T>\n            | ((qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>),\n        aliasName: string,\n    ): SelectQueryBuilder<T> {\n        const alias = this.createFromAlias(entityTarget, aliasName)\n        if (!this.expressionMap.mainAlias)\n            this.expressionMap.setMainAlias(alias)\n\n        return this as any as SelectQueryBuilder<T>\n    }\n\n    /**\n     * INNER JOINs (without selection) given subquery.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoin(\n        subQueryFactory: (\n            qb: SelectQueryBuilder<any>,\n        ) => SelectQueryBuilder<any>,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * INNER JOINs (without selection) entity's property.\n     * Given entity property should be a relation.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoin(\n        property: string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * INNER JOINs (without selection) given entity's table.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoin(\n        entity: Function | string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * INNER JOINs (without selection) given table.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoin(\n        tableName: string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * INNER JOINs (without selection).\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoin(\n        entityOrProperty:\n            | Function\n            | string\n            | ((qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>),\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this {\n        this.join(\"INNER\", entityOrProperty, alias, condition, parameters)\n        return this\n    }\n\n    /**\n     * LEFT JOINs (without selection) given subquery.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoin(\n        subQueryFactory: (\n            qb: SelectQueryBuilder<any>,\n        ) => SelectQueryBuilder<any>,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * LEFT JOINs (without selection) entity's property.\n     * Given entity property should be a relation.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoin(\n        property: string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * LEFT JOINs (without selection) entity's table.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoin(\n        entity: Function | string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * LEFT JOINs (without selection) given table.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoin(\n        tableName: string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * LEFT JOINs (without selection).\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoin(\n        entityOrProperty:\n            | Function\n            | string\n            | ((qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>),\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this {\n        this.join(\"LEFT\", entityOrProperty, alias, condition, parameters)\n        return this\n    }\n\n    /**\n     * INNER JOINs given subquery and adds all selection properties to SELECT..\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoinAndSelect(\n        subQueryFactory: (\n            qb: SelectQueryBuilder<any>,\n        ) => SelectQueryBuilder<any>,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * INNER JOINs entity's property and adds all selection properties to SELECT.\n     * Given entity property should be a relation.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoinAndSelect(\n        property: string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * INNER JOINs entity and adds all selection properties to SELECT.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoinAndSelect(\n        entity: Function | string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * INNER JOINs table and adds all selection properties to SELECT.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoinAndSelect(\n        tableName: string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * INNER JOINs and adds all selection properties to SELECT.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoinAndSelect(\n        entityOrProperty:\n            | Function\n            | string\n            | ((qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>),\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this {\n        this.addSelect(alias)\n        this.innerJoin(entityOrProperty, alias, condition, parameters)\n        return this\n    }\n\n    /**\n     * LEFT JOINs given subquery and adds all selection properties to SELECT..\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoinAndSelect(\n        subQueryFactory: (\n            qb: SelectQueryBuilder<any>,\n        ) => SelectQueryBuilder<any>,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * LEFT JOINs entity's property and adds all selection properties to SELECT.\n     * Given entity property should be a relation.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoinAndSelect(\n        property: string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * LEFT JOINs entity and adds all selection properties to SELECT.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoinAndSelect(\n        entity: Function | string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * LEFT JOINs table and adds all selection properties to SELECT.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoinAndSelect(\n        tableName: string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * LEFT JOINs and adds all selection properties to SELECT.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoinAndSelect(\n        entityOrProperty:\n            | Function\n            | string\n            | ((qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>),\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this {\n        this.addSelect(alias)\n        this.leftJoin(entityOrProperty, alias, condition, parameters)\n        return this\n    }\n\n    /**\n     * INNER JOINs given subquery, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there are multiple rows of selecting data, and mapped result will be an array.\n     * Given entity property should be a relation.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoinAndMapMany(\n        mapToProperty: string,\n        subQueryFactory: (\n            qb: SelectQueryBuilder<any>,\n        ) => SelectQueryBuilder<any>,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * INNER JOINs entity's property, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there are multiple rows of selecting data, and mapped result will be an array.\n     * Given entity property should be a relation.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoinAndMapMany(\n        mapToProperty: string,\n        property: string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * INNER JOINs entity's table, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there are multiple rows of selecting data, and mapped result will be an array.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoinAndMapMany(\n        mapToProperty: string,\n        entity: Function | string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * INNER JOINs table, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there are multiple rows of selecting data, and mapped result will be an array.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoinAndMapMany(\n        mapToProperty: string,\n        tableName: string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * INNER JOINs, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there are multiple rows of selecting data, and mapped result will be an array.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoinAndMapMany(\n        mapToProperty: string,\n        entityOrProperty:\n            | Function\n            | string\n            | ((qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>),\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this {\n        this.addSelect(alias)\n        this.join(\n            \"INNER\",\n            entityOrProperty,\n            alias,\n            condition,\n            parameters,\n            mapToProperty,\n            true,\n        )\n        return this\n    }\n\n    /**\n     * INNER JOINs given subquery, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there is a single row of selecting data, and mapped result will be a single selected value.\n     * Given entity property should be a relation.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoinAndMapOne(\n        mapToProperty: string,\n        subQueryFactory: (\n            qb: SelectQueryBuilder<any>,\n        ) => SelectQueryBuilder<any>,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n        mapAsEntity?: Function | string,\n    ): this\n\n    /**\n     * INNER JOINs entity's property, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there is a single row of selecting data, and mapped result will be a single selected value.\n     * Given entity property should be a relation.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoinAndMapOne(\n        mapToProperty: string,\n        property: string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * INNER JOINs entity's table, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there is a single row of selecting data, and mapped result will be a single selected value.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoinAndMapOne(\n        mapToProperty: string,\n        entity: Function | string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * INNER JOINs table, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there is a single row of selecting data, and mapped result will be a single selected value.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoinAndMapOne(\n        mapToProperty: string,\n        tableName: string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * INNER JOINs, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there is a single row of selecting data, and mapped result will be a single selected value.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    innerJoinAndMapOne(\n        mapToProperty: string,\n        entityOrProperty:\n            | Function\n            | string\n            | ((qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>),\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n        mapAsEntity?: Function | string,\n    ): this {\n        this.addSelect(alias)\n        this.join(\n            \"INNER\",\n            entityOrProperty,\n            alias,\n            condition,\n            parameters,\n            mapToProperty,\n            false,\n            mapAsEntity,\n        )\n        return this\n    }\n\n    /**\n     * LEFT JOINs given subquery, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there are multiple rows of selecting data, and mapped result will be an array.\n     * Given entity property should be a relation.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoinAndMapMany(\n        mapToProperty: string,\n        subQueryFactory: (\n            qb: SelectQueryBuilder<any>,\n        ) => SelectQueryBuilder<any>,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * LEFT JOINs entity's property, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there are multiple rows of selecting data, and mapped result will be an array.\n     * Given entity property should be a relation.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoinAndMapMany(\n        mapToProperty: string,\n        property: string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * LEFT JOINs entity's table, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there are multiple rows of selecting data, and mapped result will be an array.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoinAndMapMany(\n        mapToProperty: string,\n        entity: Function | string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * LEFT JOINs table, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there are multiple rows of selecting data, and mapped result will be an array.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoinAndMapMany(\n        mapToProperty: string,\n        tableName: string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * LEFT JOINs, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there are multiple rows of selecting data, and mapped result will be an array.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoinAndMapMany(\n        mapToProperty: string,\n        entityOrProperty:\n            | Function\n            | string\n            | ((qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>),\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this {\n        this.addSelect(alias)\n        this.join(\n            \"LEFT\",\n            entityOrProperty,\n            alias,\n            condition,\n            parameters,\n            mapToProperty,\n            true,\n        )\n        return this\n    }\n\n    /**\n     * LEFT JOINs given subquery, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there is a single row of selecting data, and mapped result will be a single selected value.\n     * Given entity property should be a relation.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoinAndMapOne(\n        mapToProperty: string,\n        subQueryFactory: (\n            qb: SelectQueryBuilder<any>,\n        ) => SelectQueryBuilder<any>,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n        mapAsEntity?: Function | string,\n    ): this\n\n    /**\n     * LEFT JOINs entity's property, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there is a single row of selecting data, and mapped result will be a single selected value.\n     * Given entity property should be a relation.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoinAndMapOne(\n        mapToProperty: string,\n        property: string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * LEFT JOINs entity's table, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there is a single row of selecting data, and mapped result will be a single selected value.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoinAndMapOne(\n        mapToProperty: string,\n        entity: Function | string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * LEFT JOINs table, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there is a single row of selecting data, and mapped result will be a single selected value.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoinAndMapOne(\n        mapToProperty: string,\n        tableName: string,\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n    ): this\n\n    /**\n     * LEFT JOINs, SELECTs the data returned by a join and MAPs all that data to some entity's property.\n     * This is extremely useful when you want to select some data and map it to some virtual property.\n     * It will assume that there is a single row of selecting data, and mapped result will be a single selected value.\n     * You also need to specify an alias of the joined data.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    leftJoinAndMapOne(\n        mapToProperty: string,\n        entityOrProperty:\n            | Function\n            | string\n            | ((qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>),\n        alias: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n        mapAsEntity?: Function | string,\n    ): this {\n        this.addSelect(alias)\n        this.join(\n            \"LEFT\",\n            entityOrProperty,\n            alias,\n            condition,\n            parameters,\n            mapToProperty,\n            false,\n            mapAsEntity,\n        )\n        return this\n    }\n\n    /**\n     */\n    // selectAndMap(mapToProperty: string, property: string, aliasName: string, qbFactory: ((qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>)): this;\n\n    /**\n     */\n    // selectAndMap(mapToProperty: string, entity: Function|string, aliasName: string, qbFactory: ((qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>)): this;\n\n    /**\n     */\n    // selectAndMap(mapToProperty: string, tableName: string, aliasName: string, qbFactory: ((qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>)): this;\n\n    /**\n     */\n    // selectAndMap(mapToProperty: string, entityOrProperty: Function|string, aliasName: string, qbFactory: ((qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>)): this {\n    //     const select = new SelectAttribute(this.expressionMap);\n    //     select.mapToProperty = mapToProperty;\n    //     select.entityOrProperty = entityOrProperty;\n    //     select.aliasName = aliasName;\n    //     select.qbFactory = qbFactory;\n    //     return this;\n    // }\n\n    /**\n     * LEFT JOINs relation id and maps it into some entity's property.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    loadRelationIdAndMap(\n        mapToProperty: string,\n        relationName: string,\n        options?: { disableMixedMap?: boolean },\n    ): this\n\n    /**\n     * LEFT JOINs relation id and maps it into some entity's property.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    loadRelationIdAndMap(\n        mapToProperty: string,\n        relationName: string,\n        alias: string,\n        queryBuilderFactory: (\n            qb: SelectQueryBuilder<any>,\n        ) => SelectQueryBuilder<any>,\n    ): this\n\n    /**\n     * LEFT JOINs relation id and maps it into some entity's property.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    loadRelationIdAndMap(\n        mapToProperty: string,\n        relationName: string,\n        aliasNameOrOptions?: string | { disableMixedMap?: boolean },\n        queryBuilderFactory?: (\n            qb: SelectQueryBuilder<any>,\n        ) => SelectQueryBuilder<any>,\n    ): this {\n        const relationIdAttribute = new RelationIdAttribute(this.expressionMap)\n        relationIdAttribute.mapToProperty = mapToProperty\n        relationIdAttribute.relationName = relationName\n        if (typeof aliasNameOrOptions === \"string\")\n            relationIdAttribute.alias = aliasNameOrOptions\n        if (\n            typeof aliasNameOrOptions === \"object\" &&\n            (aliasNameOrOptions as any).disableMixedMap\n        )\n            relationIdAttribute.disableMixedMap = true\n\n        relationIdAttribute.queryBuilderFactory = queryBuilderFactory\n        this.expressionMap.relationIdAttributes.push(relationIdAttribute)\n\n        if (relationIdAttribute.relation.junctionEntityMetadata) {\n            this.expressionMap.createAlias({\n                type: \"other\",\n                name: relationIdAttribute.junctionAlias,\n                metadata: relationIdAttribute.relation.junctionEntityMetadata,\n            })\n        }\n        return this\n    }\n\n    /**\n     * Counts number of entities of entity's relation and maps the value into some entity's property.\n     * Optionally, you can add condition and parameters used in condition.\n     */\n    loadRelationCountAndMap(\n        mapToProperty: string,\n        relationName: string,\n        aliasName?: string,\n        queryBuilderFactory?: (\n            qb: SelectQueryBuilder<any>,\n        ) => SelectQueryBuilder<any>,\n    ): this {\n        const relationCountAttribute = new RelationCountAttribute(\n            this.expressionMap,\n        )\n        relationCountAttribute.mapToProperty = mapToProperty\n        relationCountAttribute.relationName = relationName\n        relationCountAttribute.alias = aliasName\n        relationCountAttribute.queryBuilderFactory = queryBuilderFactory\n        this.expressionMap.relationCountAttributes.push(relationCountAttribute)\n\n        this.expressionMap.createAlias({\n            type: \"other\",\n            name: relationCountAttribute.junctionAlias,\n        })\n        if (relationCountAttribute.relation.junctionEntityMetadata) {\n            this.expressionMap.createAlias({\n                type: \"other\",\n                name: relationCountAttribute.junctionAlias,\n                metadata:\n                    relationCountAttribute.relation.junctionEntityMetadata,\n            })\n        }\n        return this\n    }\n\n    /**\n     * Loads all relation ids for all relations of the selected entity.\n     * All relation ids will be mapped to relation property themself.\n     * If array of strings is given then loads only relation ids of the given properties.\n     */\n    loadAllRelationIds(options?: {\n        relations?: string[]\n        disableMixedMap?: boolean\n    }): this {\n        // todo: add skip relations\n        this.expressionMap.mainAlias!.metadata.relations.forEach((relation) => {\n            if (\n                options !== undefined &&\n                options.relations !== undefined &&\n                options.relations.indexOf(relation.propertyPath) === -1\n            )\n                return\n\n            this.loadRelationIdAndMap(\n                this.expressionMap.mainAlias!.name +\n                    \".\" +\n                    relation.propertyPath,\n                this.expressionMap.mainAlias!.name +\n                    \".\" +\n                    relation.propertyPath,\n                options,\n            )\n        })\n        return this\n    }\n\n    /**\n     * Sets WHERE condition in the query builder.\n     * If you had previously WHERE expression defined,\n     * calling this function will override previously set WHERE conditions.\n     * Additionally you can add parameters used in where expression.\n     */\n    where(\n        where:\n            | Brackets\n            | string\n            | ((qb: this) => string)\n            | ObjectLiteral\n            | ObjectLiteral[],\n        parameters?: ObjectLiteral,\n    ): this {\n        this.expressionMap.wheres = [] // don't move this block below since computeWhereParameter can add where expressions\n        const condition = this.getWhereCondition(where)\n        if (condition) {\n            this.expressionMap.wheres = [\n                { type: \"simple\", condition: condition },\n            ]\n        }\n        if (parameters) this.setParameters(parameters)\n        return this\n    }\n\n    /**\n     * Adds new AND WHERE condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    andWhere(\n        where:\n            | string\n            | Brackets\n            | ((qb: this) => string)\n            | ObjectLiteral\n            | ObjectLiteral[],\n        parameters?: ObjectLiteral,\n    ): this {\n        this.expressionMap.wheres.push({\n            type: \"and\",\n            condition: this.getWhereCondition(where),\n        })\n        if (parameters) this.setParameters(parameters)\n        return this\n    }\n\n    /**\n     * Adds new OR WHERE condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    orWhere(\n        where:\n            | Brackets\n            | string\n            | ((qb: this) => string)\n            | ObjectLiteral\n            | ObjectLiteral[],\n        parameters?: ObjectLiteral,\n    ): this {\n        this.expressionMap.wheres.push({\n            type: \"or\",\n            condition: this.getWhereCondition(where),\n        })\n        if (parameters) this.setParameters(parameters)\n        return this\n    }\n\n    /**\n     * Sets a new where EXISTS clause\n     */\n    whereExists(subQuery: SelectQueryBuilder<any>): this {\n        return this.where(...this.getExistsCondition(subQuery))\n    }\n\n    /**\n     * Adds a new AND where EXISTS clause\n     */\n    andWhereExists(subQuery: SelectQueryBuilder<any>): this {\n        return this.andWhere(...this.getExistsCondition(subQuery))\n    }\n\n    /**\n     * Adds a new OR where EXISTS clause\n     */\n    orWhereExists(subQuery: SelectQueryBuilder<any>): this {\n        return this.orWhere(...this.getExistsCondition(subQuery))\n    }\n\n    /**\n     * Adds new AND WHERE with conditions for the given ids.\n     *\n     * Ids are mixed.\n     * It means if you have single primary key you can pass a simple id values, for example [1, 2, 3].\n     * If you have multiple primary keys you need to pass object with property names and values specified,\n     * for example [{ firstId: 1, secondId: 2 }, { firstId: 2, secondId: 3 }, ...]\n     */\n    whereInIds(ids: any | any[]): this {\n        return this.where(this.getWhereInIdsCondition(ids))\n    }\n\n    /**\n     * Adds new AND WHERE with conditions for the given ids.\n     *\n     * Ids are mixed.\n     * It means if you have single primary key you can pass a simple id values, for example [1, 2, 3].\n     * If you have multiple primary keys you need to pass object with property names and values specified,\n     * for example [{ firstId: 1, secondId: 2 }, { firstId: 2, secondId: 3 }, ...]\n     */\n    andWhereInIds(ids: any | any[]): this {\n        return this.andWhere(this.getWhereInIdsCondition(ids))\n    }\n\n    /**\n     * Adds new OR WHERE with conditions for the given ids.\n     *\n     * Ids are mixed.\n     * It means if you have single primary key you can pass a simple id values, for example [1, 2, 3].\n     * If you have multiple primary keys you need to pass object with property names and values specified,\n     * for example [{ firstId: 1, secondId: 2 }, { firstId: 2, secondId: 3 }, ...]\n     */\n    orWhereInIds(ids: any | any[]): this {\n        return this.orWhere(this.getWhereInIdsCondition(ids))\n    }\n\n    /**\n     * Sets HAVING condition in the query builder.\n     * If you had previously HAVING expression defined,\n     * calling this function will override previously set HAVING conditions.\n     * Additionally you can add parameters used in where expression.\n     */\n    having(having: string, parameters?: ObjectLiteral): this {\n        this.expressionMap.havings.push({ type: \"simple\", condition: having })\n        if (parameters) this.setParameters(parameters)\n        return this\n    }\n\n    /**\n     * Adds new AND HAVING condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    andHaving(having: string, parameters?: ObjectLiteral): this {\n        this.expressionMap.havings.push({ type: \"and\", condition: having })\n        if (parameters) this.setParameters(parameters)\n        return this\n    }\n\n    /**\n     * Adds new OR HAVING condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    orHaving(having: string, parameters?: ObjectLiteral): this {\n        this.expressionMap.havings.push({ type: \"or\", condition: having })\n        if (parameters) this.setParameters(parameters)\n        return this\n    }\n\n    /**\n     * Sets GROUP BY condition in the query builder.\n     * If you had previously GROUP BY expression defined,\n     * calling this function will override previously set GROUP BY conditions.\n     */\n    groupBy(): this\n\n    /**\n     * Sets GROUP BY condition in the query builder.\n     * If you had previously GROUP BY expression defined,\n     * calling this function will override previously set GROUP BY conditions.\n     */\n    groupBy(groupBy: string): this\n\n    /**\n     * Sets GROUP BY condition in the query builder.\n     * If you had previously GROUP BY expression defined,\n     * calling this function will override previously set GROUP BY conditions.\n     */\n    groupBy(groupBy?: string): this {\n        if (groupBy) {\n            this.expressionMap.groupBys = [groupBy]\n        } else {\n            this.expressionMap.groupBys = []\n        }\n        return this\n    }\n\n    /**\n     * Adds GROUP BY condition in the query builder.\n     */\n    addGroupBy(groupBy: string): this {\n        this.expressionMap.groupBys.push(groupBy)\n        return this\n    }\n\n    /**\n     * Enables time travelling for the current query (only supported by cockroach currently)\n     */\n    timeTravelQuery(timeTravelFn?: string | boolean): this {\n        if (this.connection.driver.options.type === \"cockroachdb\") {\n            if (timeTravelFn === undefined) {\n                this.expressionMap.timeTravel = \"follower_read_timestamp()\"\n            } else {\n                this.expressionMap.timeTravel = timeTravelFn\n            }\n        }\n\n        return this\n    }\n\n    /**\n     * Sets ORDER BY condition in the query builder.\n     * If you had previously ORDER BY expression defined,\n     * calling this function will override previously set ORDER BY conditions.\n     *\n     * Calling order by without order set will remove all previously set order bys.\n     */\n    orderBy(): this\n\n    /**\n     * Sets ORDER BY condition in the query builder.\n     * If you had previously ORDER BY expression defined,\n     * calling this function will override previously set ORDER BY conditions.\n     */\n    orderBy(\n        sort: string,\n        order?: \"ASC\" | \"DESC\",\n        nulls?: \"NULLS FIRST\" | \"NULLS LAST\",\n    ): this\n\n    /**\n     * Sets ORDER BY condition in the query builder.\n     * If you had previously ORDER BY expression defined,\n     * calling this function will override previously set ORDER BY conditions.\n     */\n    orderBy(order: OrderByCondition): this\n\n    /**\n     * Sets ORDER BY condition in the query builder.\n     * If you had previously ORDER BY expression defined,\n     * calling this function will override previously set ORDER BY conditions.\n     */\n    orderBy(\n        sort?: string | OrderByCondition,\n        order: \"ASC\" | \"DESC\" = \"ASC\",\n        nulls?: \"NULLS FIRST\" | \"NULLS LAST\",\n    ): this {\n        if (order !== undefined && order !== \"ASC\" && order !== \"DESC\")\n            throw new TypeORMError(\n                `SelectQueryBuilder.addOrderBy \"order\" can accept only \"ASC\" and \"DESC\" values.`,\n            )\n        if (\n            nulls !== undefined &&\n            nulls !== \"NULLS FIRST\" &&\n            nulls !== \"NULLS LAST\"\n        )\n            throw new TypeORMError(\n                `SelectQueryBuilder.addOrderBy \"nulls\" can accept only \"NULLS FIRST\" and \"NULLS LAST\" values.`,\n            )\n\n        if (sort) {\n            if (typeof sort === \"object\") {\n                this.expressionMap.orderBys = sort as OrderByCondition\n            } else {\n                if (nulls) {\n                    this.expressionMap.orderBys = {\n                        [sort as string]: { order, nulls },\n                    }\n                } else {\n                    this.expressionMap.orderBys = { [sort as string]: order }\n                }\n            }\n        } else {\n            this.expressionMap.orderBys = {}\n        }\n        return this\n    }\n\n    /**\n     * Adds ORDER BY condition in the query builder.\n     */\n    addOrderBy(\n        sort: string,\n        order: \"ASC\" | \"DESC\" = \"ASC\",\n        nulls?: \"NULLS FIRST\" | \"NULLS LAST\",\n    ): this {\n        if (order !== undefined && order !== \"ASC\" && order !== \"DESC\")\n            throw new TypeORMError(\n                `SelectQueryBuilder.addOrderBy \"order\" can accept only \"ASC\" and \"DESC\" values.`,\n            )\n        if (\n            nulls !== undefined &&\n            nulls !== \"NULLS FIRST\" &&\n            nulls !== \"NULLS LAST\"\n        )\n            throw new TypeORMError(\n                `SelectQueryBuilder.addOrderBy \"nulls\" can accept only \"NULLS FIRST\" and \"NULLS LAST\" values.`,\n            )\n\n        if (nulls) {\n            this.expressionMap.orderBys[sort] = { order, nulls }\n        } else {\n            this.expressionMap.orderBys[sort] = order\n        }\n        return this\n    }\n\n    /**\n     * Sets LIMIT - maximum number of rows to be selected.\n     * NOTE that it may not work as you expect if you are using joins.\n     * If you want to implement pagination, and you are having join in your query,\n     * then use the take method instead.\n     */\n    limit(limit?: number): this {\n        this.expressionMap.limit = this.normalizeNumber(limit)\n        if (\n            this.expressionMap.limit !== undefined &&\n            isNaN(this.expressionMap.limit)\n        )\n            throw new TypeORMError(\n                `Provided \"limit\" value is not a number. Please provide a numeric value.`,\n            )\n\n        return this\n    }\n\n    /**\n     * Sets OFFSET - selection offset.\n     * NOTE that it may not work as you expect if you are using joins.\n     * If you want to implement pagination, and you are having join in your query,\n     * then use the skip method instead.\n     */\n    offset(offset?: number): this {\n        this.expressionMap.offset = this.normalizeNumber(offset)\n        if (\n            this.expressionMap.offset !== undefined &&\n            isNaN(this.expressionMap.offset)\n        )\n            throw new TypeORMError(\n                `Provided \"offset\" value is not a number. Please provide a numeric value.`,\n            )\n\n        return this\n    }\n\n    /**\n     * Sets maximal number of entities to take.\n     */\n    take(take?: number): this {\n        this.expressionMap.take = this.normalizeNumber(take)\n        if (\n            this.expressionMap.take !== undefined &&\n            isNaN(this.expressionMap.take)\n        )\n            throw new TypeORMError(\n                `Provided \"take\" value is not a number. Please provide a numeric value.`,\n            )\n\n        return this\n    }\n\n    /**\n     * Sets number of entities to skip.\n     */\n    skip(skip?: number): this {\n        this.expressionMap.skip = this.normalizeNumber(skip)\n        if (\n            this.expressionMap.skip !== undefined &&\n            isNaN(this.expressionMap.skip)\n        )\n            throw new TypeORMError(\n                `Provided \"skip\" value is not a number. Please provide a numeric value.`,\n            )\n\n        return this\n    }\n\n    /**\n     * Set certain index to be used by the query.\n     *\n     * @param index Name of index to be used.\n     */\n    useIndex(index: string): this {\n        this.expressionMap.useIndex = index\n\n        return this\n    }\n\n    /**\n     * Sets locking mode.\n     */\n    setLock(lockMode: \"optimistic\", lockVersion: number | Date): this\n\n    /**\n     * Sets locking mode.\n     */\n    setLock(\n        lockMode:\n            | \"pessimistic_read\"\n            | \"pessimistic_write\"\n            | \"dirty_read\"\n            /*\n                \"pessimistic_partial_write\" and \"pessimistic_write_or_fail\" are deprecated and\n                will be removed in a future version.\n\n                Use setOnLocked instead.\n             */\n            | \"pessimistic_partial_write\"\n            | \"pessimistic_write_or_fail\"\n            | \"for_no_key_update\"\n            | \"for_key_share\",\n        lockVersion?: undefined,\n        lockTables?: string[],\n    ): this\n\n    /**\n     * Sets locking mode.\n     */\n    setLock(\n        lockMode:\n            | \"optimistic\"\n            | \"pessimistic_read\"\n            | \"pessimistic_write\"\n            | \"dirty_read\"\n            /*\n                \"pessimistic_partial_write\" and \"pessimistic_write_or_fail\" are deprecated and\n                will be removed in a future version.\n\n                Use setOnLocked instead.\n             */\n            | \"pessimistic_partial_write\"\n            | \"pessimistic_write_or_fail\"\n            | \"for_no_key_update\"\n            | \"for_key_share\",\n        lockVersion?: number | Date,\n        lockTables?: string[],\n    ): this {\n        this.expressionMap.lockMode = lockMode\n        this.expressionMap.lockVersion = lockVersion\n        this.expressionMap.lockTables = lockTables\n        return this\n    }\n\n    /**\n     * Sets lock handling by adding NO WAIT or SKIP LOCKED.\n     */\n    setOnLocked(onLocked: \"nowait\" | \"skip_locked\"): this {\n        this.expressionMap.onLocked = onLocked\n        return this\n    }\n\n    /**\n     * Disables the global condition of \"non-deleted\" for the entity with delete date columns.\n     */\n    withDeleted(): this {\n        this.expressionMap.withDeleted = true\n        return this\n    }\n\n    /**\n     * Gets first raw result returned by execution of generated query builder sql.\n     */\n    async getRawOne<T = any>(): Promise<T | undefined> {\n        return (await this.getRawMany())[0]\n    }\n\n    /**\n     * Gets all raw results returned by execution of generated query builder sql.\n     */\n    async getRawMany<T = any>(): Promise<T[]> {\n        if (this.expressionMap.lockMode === \"optimistic\")\n            throw new OptimisticLockCanNotBeUsedError()\n\n        this.expressionMap.queryEntity = false\n        const queryRunner = this.obtainQueryRunner()\n        let transactionStartedByUs: boolean = false\n        try {\n            // start transaction if it was enabled\n            if (\n                this.expressionMap.useTransaction === true &&\n                queryRunner.isTransactionActive === false\n            ) {\n                await queryRunner.startTransaction()\n                transactionStartedByUs = true\n            }\n\n            const results = await this.loadRawResults(queryRunner)\n\n            // close transaction if we started it\n            if (transactionStartedByUs) {\n                await queryRunner.commitTransaction()\n            }\n\n            return results\n        } catch (error) {\n            // rollback transaction if we started it\n            if (transactionStartedByUs) {\n                try {\n                    await queryRunner.rollbackTransaction()\n                } catch (rollbackError) {}\n            }\n            throw error\n        } finally {\n            if (queryRunner !== this.queryRunner) {\n                // means we created our own query runner\n                await queryRunner.release()\n            }\n        }\n    }\n\n    /**\n     * Executes sql generated by query builder and returns object with raw results and entities created from them.\n     */\n    async getRawAndEntities<T = any>(): Promise<{\n        entities: Entity[]\n        raw: T[]\n    }> {\n        const queryRunner = this.obtainQueryRunner()\n        let transactionStartedByUs: boolean = false\n        try {\n            // start transaction if it was enabled\n            if (\n                this.expressionMap.useTransaction === true &&\n                queryRunner.isTransactionActive === false\n            ) {\n                await queryRunner.startTransaction()\n                transactionStartedByUs = true\n            }\n\n            this.expressionMap.queryEntity = true\n            const results = await this.executeEntitiesAndRawResults(queryRunner)\n\n            // close transaction if we started it\n            if (transactionStartedByUs) {\n                await queryRunner.commitTransaction()\n            }\n\n            return results\n        } catch (error) {\n            // rollback transaction if we started it\n            if (transactionStartedByUs) {\n                try {\n                    await queryRunner.rollbackTransaction()\n                } catch (rollbackError) {}\n            }\n            throw error\n        } finally {\n            if (queryRunner !== this.queryRunner)\n                // means we created our own query runner\n                await queryRunner.release()\n        }\n    }\n\n    /**\n     * Gets single entity returned by execution of generated query builder sql.\n     */\n    async getOne(): Promise<Entity | null> {\n        const results = await this.getRawAndEntities()\n        const result = results.entities[0] as any\n\n        if (\n            result &&\n            this.expressionMap.lockMode === \"optimistic\" &&\n            this.expressionMap.lockVersion\n        ) {\n            const metadata = this.expressionMap.mainAlias!.metadata\n\n            if (this.expressionMap.lockVersion instanceof Date) {\n                const actualVersion =\n                    metadata.updateDateColumn!.getEntityValue(result) // what if columns arent set?\n                if (\n                    actualVersion.getTime() !==\n                    this.expressionMap.lockVersion.getTime()\n                )\n                    throw new OptimisticLockVersionMismatchError(\n                        metadata.name,\n                        this.expressionMap.lockVersion,\n                        actualVersion,\n                    )\n            } else {\n                const actualVersion =\n                    metadata.versionColumn!.getEntityValue(result) // what if columns arent set?\n                if (actualVersion !== this.expressionMap.lockVersion)\n                    throw new OptimisticLockVersionMismatchError(\n                        metadata.name,\n                        this.expressionMap.lockVersion,\n                        actualVersion,\n                    )\n            }\n        }\n\n        if (result === undefined) {\n            return null\n        }\n        return result\n    }\n\n    /**\n     * Gets the first entity returned by execution of generated query builder sql or rejects the returned promise on error.\n     */\n    async getOneOrFail(): Promise<Entity> {\n        const entity = await this.getOne()\n\n        if (!entity) {\n            throw new EntityNotFoundError(\n                this.expressionMap.mainAlias!.target,\n                this.expressionMap.parameters,\n            )\n        }\n\n        return entity\n    }\n\n    /**\n     * Gets entities returned by execution of generated query builder sql.\n     */\n    async getMany(): Promise<Entity[]> {\n        if (this.expressionMap.lockMode === \"optimistic\")\n            throw new OptimisticLockCanNotBeUsedError()\n\n        const results = await this.getRawAndEntities()\n        return results.entities\n    }\n\n    /**\n     * Gets count - number of entities selected by sql generated by this query builder.\n     * Count excludes all limitations set by offset, limit, skip, and take.\n     */\n    async getCount(): Promise<number> {\n        if (this.expressionMap.lockMode === \"optimistic\")\n            throw new OptimisticLockCanNotBeUsedError()\n\n        const queryRunner = this.obtainQueryRunner()\n        let transactionStartedByUs: boolean = false\n        try {\n            // start transaction if it was enabled\n            if (\n                this.expressionMap.useTransaction === true &&\n                queryRunner.isTransactionActive === false\n            ) {\n                await queryRunner.startTransaction()\n                transactionStartedByUs = true\n            }\n\n            this.expressionMap.queryEntity = false\n            const results = await this.executeCountQuery(queryRunner)\n\n            // close transaction if we started it\n            if (transactionStartedByUs) {\n                await queryRunner.commitTransaction()\n            }\n\n            return results\n        } catch (error) {\n            // rollback transaction if we started it\n            if (transactionStartedByUs) {\n                try {\n                    await queryRunner.rollbackTransaction()\n                } catch (rollbackError) {}\n            }\n            throw error\n        } finally {\n            if (queryRunner !== this.queryRunner)\n                // means we created our own query runner\n                await queryRunner.release()\n        }\n    }\n\n    /**\n     * Gets exists\n     * Returns whether any rows exists matching current query.\n     */\n    async getExists(): Promise<boolean> {\n        if (this.expressionMap.lockMode === \"optimistic\")\n            throw new OptimisticLockCanNotBeUsedError()\n\n        const queryRunner = this.obtainQueryRunner()\n        let transactionStartedByUs: boolean = false\n        try {\n            // start transaction if it was enabled\n            if (\n                this.expressionMap.useTransaction === true &&\n                queryRunner.isTransactionActive === false\n            ) {\n                await queryRunner.startTransaction()\n                transactionStartedByUs = true\n            }\n\n            this.expressionMap.queryEntity = false\n            const results = await this.executeExistsQuery(queryRunner)\n\n            // close transaction if we started it\n            if (transactionStartedByUs) {\n                await queryRunner.commitTransaction()\n            }\n\n            return results\n        } catch (error) {\n            // rollback transaction if we started it\n            if (transactionStartedByUs) {\n                try {\n                    await queryRunner.rollbackTransaction()\n                } catch (rollbackError) {}\n            }\n            throw error\n        } finally {\n            if (queryRunner !== this.queryRunner)\n                // means we created our own query runner\n                await queryRunner.release()\n        }\n    }\n\n    /**\n     * Executes built SQL query and returns entities and overall entities count (without limitation).\n     * This method is useful to build pagination.\n     */\n    async getManyAndCount(): Promise<[Entity[], number]> {\n        if (this.expressionMap.lockMode === \"optimistic\")\n            throw new OptimisticLockCanNotBeUsedError()\n\n        const queryRunner = this.obtainQueryRunner()\n        let transactionStartedByUs: boolean = false\n        try {\n            // start transaction if it was enabled\n            if (\n                this.expressionMap.useTransaction === true &&\n                queryRunner.isTransactionActive === false\n            ) {\n                await queryRunner.startTransaction()\n                transactionStartedByUs = true\n            }\n\n            this.expressionMap.queryEntity = true\n            const entitiesAndRaw = await this.executeEntitiesAndRawResults(\n                queryRunner,\n            )\n            this.expressionMap.queryEntity = false\n            const cacheId = this.expressionMap.cacheId\n            // Creates a new cacheId for the count query, or it will retreive the above query results\n            // and count will return 0.\n            this.expressionMap.cacheId = cacheId ? `${cacheId}-count` : cacheId\n            const count = await this.executeCountQuery(queryRunner)\n            const results: [Entity[], number] = [entitiesAndRaw.entities, count]\n\n            // close transaction if we started it\n            if (transactionStartedByUs) {\n                await queryRunner.commitTransaction()\n            }\n\n            return results\n        } catch (error) {\n            // rollback transaction if we started it\n            if (transactionStartedByUs) {\n                try {\n                    await queryRunner.rollbackTransaction()\n                } catch (rollbackError) {}\n            }\n            throw error\n        } finally {\n            if (queryRunner !== this.queryRunner)\n                // means we created our own query runner\n                await queryRunner.release()\n        }\n    }\n\n    /**\n     * Executes built SQL query and returns raw data stream.\n     */\n    async stream(): Promise<ReadStream> {\n        this.expressionMap.queryEntity = false\n        const [sql, parameters] = this.getQueryAndParameters()\n        const queryRunner = this.obtainQueryRunner()\n        let transactionStartedByUs: boolean = false\n        try {\n            // start transaction if it was enabled\n            if (\n                this.expressionMap.useTransaction === true &&\n                queryRunner.isTransactionActive === false\n            ) {\n                await queryRunner.startTransaction()\n                transactionStartedByUs = true\n            }\n\n            const releaseFn = () => {\n                if (queryRunner !== this.queryRunner)\n                    // means we created our own query runner\n                    return queryRunner.release()\n                return\n            }\n            const results = queryRunner.stream(\n                sql,\n                parameters,\n                releaseFn,\n                releaseFn,\n            )\n\n            // close transaction if we started it\n            if (transactionStartedByUs) {\n                await queryRunner.commitTransaction()\n            }\n\n            return results\n        } catch (error) {\n            // rollback transaction if we started it\n            if (transactionStartedByUs) {\n                try {\n                    await queryRunner.rollbackTransaction()\n                } catch (rollbackError) {}\n            }\n            throw error\n        }\n    }\n\n    /**\n     * Enables or disables query result caching.\n     */\n    cache(enabled: boolean): this\n\n    /**\n     * Enables query result caching and sets in milliseconds in which cache will expire.\n     * If not set then global caching time will be used.\n     */\n    cache(milliseconds: number): this\n\n    /**\n     * Enables query result caching and sets cache id and milliseconds in which cache will expire.\n     */\n    cache(id: any, milliseconds?: number): this\n\n    /**\n     * Enables or disables query result caching.\n     */\n    cache(\n        enabledOrMillisecondsOrId: boolean | number | string,\n        maybeMilliseconds?: number,\n    ): this {\n        if (typeof enabledOrMillisecondsOrId === \"boolean\") {\n            this.expressionMap.cache = enabledOrMillisecondsOrId\n        } else if (typeof enabledOrMillisecondsOrId === \"number\") {\n            this.expressionMap.cache = true\n            this.expressionMap.cacheDuration = enabledOrMillisecondsOrId\n        } else if (\n            typeof enabledOrMillisecondsOrId === \"string\" ||\n            typeof enabledOrMillisecondsOrId === \"number\"\n        ) {\n            this.expressionMap.cache = true\n            this.expressionMap.cacheId = enabledOrMillisecondsOrId\n        }\n\n        if (maybeMilliseconds) {\n            this.expressionMap.cacheDuration = maybeMilliseconds\n        }\n\n        return this\n    }\n\n    /**\n     * Sets extra options that can be used to configure how query builder works.\n     */\n    setOption(option: SelectQueryBuilderOption): this {\n        this.expressionMap.options.push(option)\n        return this\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    protected join(\n        direction: \"INNER\" | \"LEFT\",\n        entityOrProperty:\n            | Function\n            | string\n            | ((qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>),\n        aliasName: string,\n        condition?: string,\n        parameters?: ObjectLiteral,\n        mapToProperty?: string,\n        isMappingMany?: boolean,\n        mapAsEntity?: Function | string,\n    ): void {\n        if (parameters) {\n            this.setParameters(parameters)\n        }\n\n        const joinAttribute = new JoinAttribute(\n            this.connection,\n            this.expressionMap,\n        )\n        joinAttribute.direction = direction\n        joinAttribute.mapAsEntity = mapAsEntity\n        joinAttribute.mapToProperty = mapToProperty\n        joinAttribute.isMappingMany = isMappingMany\n        joinAttribute.entityOrProperty = entityOrProperty // relationName\n        joinAttribute.condition = condition // joinInverseSideCondition\n        // joinAttribute.junctionAlias = joinAttribute.relation.isOwning ? parentAlias + \"_\" + destinationTableAlias : destinationTableAlias + \"_\" + parentAlias;\n        this.expressionMap.joinAttributes.push(joinAttribute)\n\n        const joinAttributeMetadata = joinAttribute.metadata\n        if (joinAttributeMetadata) {\n            if (\n                joinAttributeMetadata.deleteDateColumn &&\n                !this.expressionMap.withDeleted\n            ) {\n                const conditionDeleteColumn = `${aliasName}.${joinAttributeMetadata.deleteDateColumn.propertyName} IS NULL`\n                joinAttribute.condition = joinAttribute.condition\n                    ? ` ${joinAttribute.condition} AND ${conditionDeleteColumn}`\n                    : `${conditionDeleteColumn}`\n            }\n            // todo: find and set metadata right there?\n            joinAttribute.alias = this.expressionMap.createAlias({\n                type: \"join\",\n                name: aliasName,\n                metadata: joinAttributeMetadata,\n            })\n            if (\n                joinAttribute.relation &&\n                joinAttribute.relation.junctionEntityMetadata\n            ) {\n                this.expressionMap.createAlias({\n                    type: \"join\",\n                    name: joinAttribute.junctionAlias,\n                    metadata: joinAttribute.relation.junctionEntityMetadata,\n                })\n            }\n        } else {\n            let subQuery: string = \"\"\n            if (typeof entityOrProperty === \"function\") {\n                const subQueryBuilder: SelectQueryBuilder<any> = (\n                    entityOrProperty as any\n                )((this as any as SelectQueryBuilder<any>).subQuery())\n                this.setParameters(subQueryBuilder.getParameters())\n                subQuery = subQueryBuilder.getQuery()\n            } else {\n                subQuery = entityOrProperty\n            }\n            const isSubQuery =\n                typeof entityOrProperty === \"function\" ||\n                (entityOrProperty.substr(0, 1) === \"(\" &&\n                    entityOrProperty.substr(-1) === \")\")\n            joinAttribute.alias = this.expressionMap.createAlias({\n                type: \"join\",\n                name: aliasName,\n                tablePath:\n                    isSubQuery === false\n                        ? (entityOrProperty as string)\n                        : undefined,\n                subQuery: isSubQuery === true ? subQuery : undefined,\n            })\n        }\n    }\n\n    /**\n     * Creates \"SELECT FROM\" part of SQL query.\n     */\n    protected createSelectExpression() {\n        if (!this.expressionMap.mainAlias)\n            throw new TypeORMError(\n                \"Cannot build query because main alias is not set (call qb#from method)\",\n            )\n\n        // todo throw exception if selects or from is missing\n\n        const allSelects: SelectQuery[] = []\n        const excludedSelects: SelectQuery[] = []\n\n        if (this.expressionMap.mainAlias.hasMetadata) {\n            const metadata = this.expressionMap.mainAlias.metadata\n            allSelects.push(\n                ...this.buildEscapedEntityColumnSelects(\n                    this.expressionMap.mainAlias.name,\n                    metadata,\n                ),\n            )\n            excludedSelects.push(\n                ...this.findEntityColumnSelects(\n                    this.expressionMap.mainAlias.name,\n                    metadata,\n                ),\n            )\n        }\n\n        // add selects from joins\n        this.expressionMap.joinAttributes.forEach((join) => {\n            if (join.metadata) {\n                allSelects.push(\n                    ...this.buildEscapedEntityColumnSelects(\n                        join.alias.name!,\n                        join.metadata,\n                    ),\n                )\n                excludedSelects.push(\n                    ...this.findEntityColumnSelects(\n                        join.alias.name!,\n                        join.metadata,\n                    ),\n                )\n            } else {\n                const hasMainAlias = this.expressionMap.selects.some(\n                    (select) => select.selection === join.alias.name,\n                )\n                if (hasMainAlias) {\n                    allSelects.push({\n                        selection: this.escape(join.alias.name!) + \".*\",\n                    })\n                    const excludedSelect = this.expressionMap.selects.find(\n                        (select) => select.selection === join.alias.name,\n                    )\n                    excludedSelects.push(excludedSelect!)\n                }\n            }\n        })\n\n        // add all other selects\n        this.expressionMap.selects\n            .filter((select) => excludedSelects.indexOf(select) === -1)\n            .forEach((select) =>\n                allSelects.push({\n                    selection: this.replacePropertyNames(select.selection),\n                    aliasName: select.aliasName,\n                }),\n            )\n\n        // if still selection is empty, then simply set it to all (*)\n        if (allSelects.length === 0) allSelects.push({ selection: \"*\" })\n\n        // Use certain index\n        let useIndex: string = \"\"\n        if (this.expressionMap.useIndex) {\n            if (DriverUtils.isMySQLFamily(this.connection.driver)) {\n                useIndex = ` USE INDEX (${this.expressionMap.useIndex})`\n            }\n        }\n\n        // create a selection query\n        const froms = this.expressionMap.aliases\n            .filter(\n                (alias) =>\n                    alias.type === \"from\" &&\n                    (alias.tablePath || alias.subQuery),\n            )\n            .map((alias) => {\n                if (alias.subQuery)\n                    return alias.subQuery + \" \" + this.escape(alias.name)\n\n                return (\n                    this.getTableName(alias.tablePath!) +\n                    \" \" +\n                    this.escape(alias.name)\n                )\n            })\n\n        const select = this.createSelectDistinctExpression()\n        const selection = allSelects\n            .map(\n                (select) =>\n                    select.selection +\n                    (select.aliasName\n                        ? \" AS \" + this.escape(select.aliasName)\n                        : \"\"),\n            )\n            .join(\", \")\n\n        return (\n            select +\n            selection +\n            \" FROM \" +\n            froms.join(\", \") +\n            this.createTableLockExpression() +\n            useIndex\n        )\n    }\n\n    /**\n     * Creates select | select distinct part of SQL query.\n     */\n    protected createSelectDistinctExpression(): string {\n        const { selectDistinct, selectDistinctOn, maxExecutionTime } =\n            this.expressionMap\n        const { driver } = this.connection\n\n        let select = \"SELECT \"\n\n        if (maxExecutionTime > 0) {\n            if (DriverUtils.isMySQLFamily(driver)) {\n                select += `/*+ MAX_EXECUTION_TIME(${this.expressionMap.maxExecutionTime}) */ `\n            }\n        }\n\n        if (\n            DriverUtils.isPostgresFamily(driver) &&\n            selectDistinctOn.length > 0\n        ) {\n            const selectDistinctOnMap = selectDistinctOn\n                .map((on) => this.replacePropertyNames(on))\n                .join(\", \")\n\n            select = `SELECT DISTINCT ON (${selectDistinctOnMap}) `\n        } else if (selectDistinct) {\n            select = \"SELECT DISTINCT \"\n        }\n\n        return select\n    }\n\n    /**\n     * Creates \"JOIN\" part of SQL query.\n     */\n    protected createJoinExpression(): string {\n        // examples:\n        // select from owning side\n        // qb.select(\"post\")\n        //     .leftJoinAndSelect(\"post.category\", \"category\");\n        // select from non-owning side\n        // qb.select(\"category\")\n        //     .leftJoinAndSelect(\"category.post\", \"post\");\n\n        const joins = this.expressionMap.joinAttributes.map((joinAttr) => {\n            const relation = joinAttr.relation\n            const destinationTableName = joinAttr.tablePath\n            const destinationTableAlias = joinAttr.alias.name\n            let appendedCondition = joinAttr.condition\n                ? \" AND (\" + joinAttr.condition + \")\"\n                : \"\"\n            const parentAlias = joinAttr.parentAlias\n\n            // if join was build without relation (e.g. without \"post.category\") then it means that we have direct\n            // table to join, without junction table involved. This means we simply join direct table.\n            if (!parentAlias || !relation) {\n                const destinationJoin = joinAttr.alias.subQuery\n                    ? joinAttr.alias.subQuery\n                    : this.getTableName(destinationTableName)\n                return (\n                    \" \" +\n                    joinAttr.direction +\n                    \" JOIN \" +\n                    destinationJoin +\n                    \" \" +\n                    this.escape(destinationTableAlias) +\n                    this.createTableLockExpression() +\n                    (joinAttr.condition\n                        ? \" ON \" + this.replacePropertyNames(joinAttr.condition)\n                        : \"\")\n                )\n            }\n\n            // if real entity relation is involved\n            if (relation.isManyToOne || relation.isOneToOneOwner) {\n                // JOIN `category` `category` ON `category`.`id` = `post`.`categoryId`\n                const condition = relation.joinColumns\n                    .map((joinColumn) => {\n                        return (\n                            destinationTableAlias +\n                            \".\" +\n                            joinColumn.referencedColumn!.propertyPath +\n                            \"=\" +\n                            parentAlias +\n                            \".\" +\n                            relation.propertyPath +\n                            \".\" +\n                            joinColumn.referencedColumn!.propertyPath\n                        )\n                    })\n                    .join(\" AND \")\n\n                return (\n                    \" \" +\n                    joinAttr.direction +\n                    \" JOIN \" +\n                    this.getTableName(destinationTableName) +\n                    \" \" +\n                    this.escape(destinationTableAlias) +\n                    this.createTableLockExpression() +\n                    \" ON \" +\n                    this.replacePropertyNames(condition + appendedCondition)\n                )\n            } else if (relation.isOneToMany || relation.isOneToOneNotOwner) {\n                // JOIN `post` `post` ON `post`.`categoryId` = `category`.`id`\n                const condition = relation\n                    .inverseRelation!.joinColumns.map((joinColumn) => {\n                        if (\n                            relation.inverseEntityMetadata.tableType ===\n                                \"entity-child\" &&\n                            relation.inverseEntityMetadata.discriminatorColumn\n                        ) {\n                            appendedCondition +=\n                                \" AND \" +\n                                destinationTableAlias +\n                                \".\" +\n                                relation.inverseEntityMetadata\n                                    .discriminatorColumn.databaseName +\n                                \"='\" +\n                                relation.inverseEntityMetadata\n                                    .discriminatorValue +\n                                \"'\"\n                        }\n\n                        return (\n                            destinationTableAlias +\n                            \".\" +\n                            relation.inverseRelation!.propertyPath +\n                            \".\" +\n                            joinColumn.referencedColumn!.propertyPath +\n                            \"=\" +\n                            parentAlias +\n                            \".\" +\n                            joinColumn.referencedColumn!.propertyPath\n                        )\n                    })\n                    .join(\" AND \")\n\n                if (!condition)\n                    throw new TypeORMError(\n                        `Relation ${relation.entityMetadata.name}.${relation.propertyName} does not have join columns.`,\n                    )\n\n                return (\n                    \" \" +\n                    joinAttr.direction +\n                    \" JOIN \" +\n                    this.getTableName(destinationTableName) +\n                    \" \" +\n                    this.escape(destinationTableAlias) +\n                    this.createTableLockExpression() +\n                    \" ON \" +\n                    this.replacePropertyNames(condition + appendedCondition)\n                )\n            } else {\n                // means many-to-many\n                const junctionTableName =\n                    relation.junctionEntityMetadata!.tablePath\n\n                const junctionAlias = joinAttr.junctionAlias\n                let junctionCondition = \"\",\n                    destinationCondition = \"\"\n\n                if (relation.isOwning) {\n                    junctionCondition = relation.joinColumns\n                        .map((joinColumn) => {\n                            // `post_category`.`postId` = `post`.`id`\n                            return (\n                                junctionAlias +\n                                \".\" +\n                                joinColumn.propertyPath +\n                                \"=\" +\n                                parentAlias +\n                                \".\" +\n                                joinColumn.referencedColumn!.propertyPath\n                            )\n                        })\n                        .join(\" AND \")\n\n                    destinationCondition = relation.inverseJoinColumns\n                        .map((joinColumn) => {\n                            // `category`.`id` = `post_category`.`categoryId`\n                            return (\n                                destinationTableAlias +\n                                \".\" +\n                                joinColumn.referencedColumn!.propertyPath +\n                                \"=\" +\n                                junctionAlias +\n                                \".\" +\n                                joinColumn.propertyPath\n                            )\n                        })\n                        .join(\" AND \")\n                } else {\n                    junctionCondition = relation\n                        .inverseRelation!.inverseJoinColumns.map(\n                            (joinColumn) => {\n                                // `post_category`.`categoryId` = `category`.`id`\n                                return (\n                                    junctionAlias +\n                                    \".\" +\n                                    joinColumn.propertyPath +\n                                    \"=\" +\n                                    parentAlias +\n                                    \".\" +\n                                    joinColumn.referencedColumn!.propertyPath\n                                )\n                            },\n                        )\n                        .join(\" AND \")\n\n                    destinationCondition = relation\n                        .inverseRelation!.joinColumns.map((joinColumn) => {\n                            // `post`.`id` = `post_category`.`postId`\n                            return (\n                                destinationTableAlias +\n                                \".\" +\n                                joinColumn.referencedColumn!.propertyPath +\n                                \"=\" +\n                                junctionAlias +\n                                \".\" +\n                                joinColumn.propertyPath\n                            )\n                        })\n                        .join(\" AND \")\n                }\n\n                return (\n                    \" \" +\n                    joinAttr.direction +\n                    \" JOIN \" +\n                    this.getTableName(junctionTableName) +\n                    \" \" +\n                    this.escape(junctionAlias) +\n                    this.createTableLockExpression() +\n                    \" ON \" +\n                    this.replacePropertyNames(junctionCondition) +\n                    \" \" +\n                    joinAttr.direction +\n                    \" JOIN \" +\n                    this.getTableName(destinationTableName) +\n                    \" \" +\n                    this.escape(destinationTableAlias) +\n                    this.createTableLockExpression() +\n                    \" ON \" +\n                    this.replacePropertyNames(\n                        destinationCondition + appendedCondition,\n                    )\n                )\n            }\n        })\n\n        return joins.join(\" \")\n    }\n\n    /**\n     * Creates \"GROUP BY\" part of SQL query.\n     */\n    protected createGroupByExpression() {\n        if (!this.expressionMap.groupBys || !this.expressionMap.groupBys.length)\n            return \"\"\n        return (\n            \" GROUP BY \" +\n            this.replacePropertyNames(this.expressionMap.groupBys.join(\", \"))\n        )\n    }\n\n    /**\n     * Creates \"ORDER BY\" part of SQL query.\n     */\n    protected createOrderByExpression() {\n        const orderBys = this.expressionMap.allOrderBys\n        if (Object.keys(orderBys).length === 0) return \"\"\n\n        return (\n            \" ORDER BY \" +\n            Object.keys(orderBys)\n                .map((columnName) => {\n                    const orderValue =\n                        typeof orderBys[columnName] === \"string\"\n                            ? orderBys[columnName]\n                            : (orderBys[columnName] as any).order +\n                              \" \" +\n                              (orderBys[columnName] as any).nulls\n                    const selection = this.expressionMap.selects.find(\n                        (s) => s.selection === columnName,\n                    )\n                    if (\n                        selection &&\n                        !selection.aliasName &&\n                        columnName.indexOf(\".\") !== -1\n                    ) {\n                        const criteriaParts = columnName.split(\".\")\n                        const aliasName = criteriaParts[0]\n                        const propertyPath = criteriaParts.slice(1).join(\".\")\n                        const alias = this.expressionMap.aliases.find(\n                            (alias) => alias.name === aliasName,\n                        )\n                        if (alias) {\n                            const column =\n                                alias.metadata.findColumnWithPropertyPath(\n                                    propertyPath,\n                                )\n                            if (column) {\n                                const orderAlias = DriverUtils.buildAlias(\n                                    this.connection.driver,\n                                    undefined,\n                                    aliasName,\n                                    column.databaseName,\n                                )\n                                return (\n                                    this.escape(orderAlias) + \" \" + orderValue\n                                )\n                            }\n                        }\n                    }\n\n                    return (\n                        this.replacePropertyNames(columnName) + \" \" + orderValue\n                    )\n                })\n                .join(\", \")\n        )\n    }\n\n    /**\n     * Creates \"LIMIT\" and \"OFFSET\" parts of SQL query.\n     */\n    protected createLimitOffsetExpression(): string {\n        // in the case if nothing is joined in the query builder we don't need to make two requests to get paginated results\n        // we can use regular limit / offset, that's why we add offset and limit construction here based on skip and take values\n        let offset: number | undefined = this.expressionMap.offset,\n            limit: number | undefined = this.expressionMap.limit\n        if (\n            !offset &&\n            !limit &&\n            this.expressionMap.joinAttributes.length === 0\n        ) {\n            offset = this.expressionMap.skip\n            limit = this.expressionMap.take\n        }\n\n        if (this.connection.driver.options.type === \"mssql\") {\n            // Due to a limitation in SQL Server's parser implementation it does not support using\n            // OFFSET or FETCH NEXT without an ORDER BY clause being provided. In cases where the\n            // user does not request one we insert a dummy ORDER BY that does nothing and should\n            // have no effect on the query planner or on the order of the results returned.\n            // https://dba.stackexchange.com/a/193799\n            let prefix = \"\"\n            if (\n                (limit || offset) &&\n                Object.keys(this.expressionMap.allOrderBys).length <= 0\n            ) {\n                prefix = \" ORDER BY (SELECT NULL)\"\n            }\n\n            if (limit && offset)\n                return (\n                    prefix +\n                    \" OFFSET \" +\n                    offset +\n                    \" ROWS FETCH NEXT \" +\n                    limit +\n                    \" ROWS ONLY\"\n                )\n            if (limit)\n                return (\n                    prefix + \" OFFSET 0 ROWS FETCH NEXT \" + limit + \" ROWS ONLY\"\n                )\n            if (offset) return prefix + \" OFFSET \" + offset + \" ROWS\"\n        } else if (\n            DriverUtils.isMySQLFamily(this.connection.driver) ||\n            this.connection.driver.options.type === \"aurora-mysql\" ||\n            this.connection.driver.options.type === \"sap\" ||\n            this.connection.driver.options.type === \"spanner\"\n        ) {\n            if (limit && offset) return \" LIMIT \" + limit + \" OFFSET \" + offset\n            if (limit) return \" LIMIT \" + limit\n            if (offset) throw new OffsetWithoutLimitNotSupportedError()\n        } else if (DriverUtils.isSQLiteFamily(this.connection.driver)) {\n            if (limit && offset) return \" LIMIT \" + limit + \" OFFSET \" + offset\n            if (limit) return \" LIMIT \" + limit\n            if (offset) return \" LIMIT -1 OFFSET \" + offset\n        } else if (this.connection.driver.options.type === \"oracle\") {\n            if (limit && offset)\n                return (\n                    \" OFFSET \" +\n                    offset +\n                    \" ROWS FETCH NEXT \" +\n                    limit +\n                    \" ROWS ONLY\"\n                )\n            if (limit) return \" FETCH NEXT \" + limit + \" ROWS ONLY\"\n            if (offset) return \" OFFSET \" + offset + \" ROWS\"\n        } else {\n            if (limit && offset) return \" LIMIT \" + limit + \" OFFSET \" + offset\n            if (limit) return \" LIMIT \" + limit\n            if (offset) return \" OFFSET \" + offset\n        }\n\n        return \"\"\n    }\n\n    /**\n     * Creates \"LOCK\" part of SELECT Query after table Clause\n     * ex.\n     *  SELECT 1\n     *  FROM USER U WITH (NOLOCK)\n     *  JOIN ORDER O WITH (NOLOCK)\n     *      ON U.ID=O.OrderID\n     */\n    private createTableLockExpression(): string {\n        if (this.connection.driver.options.type === \"mssql\") {\n            switch (this.expressionMap.lockMode) {\n                case \"pessimistic_read\":\n                    return \" WITH (HOLDLOCK, ROWLOCK)\"\n                case \"pessimistic_write\":\n                    return \" WITH (UPDLOCK, ROWLOCK)\"\n                case \"dirty_read\":\n                    return \" WITH (NOLOCK)\"\n            }\n        }\n\n        return \"\"\n    }\n\n    /**\n     * Creates \"LOCK\" part of SQL query.\n     */\n    protected createLockExpression(): string {\n        const driver = this.connection.driver\n\n        let lockTablesClause = \"\"\n\n        if (this.expressionMap.lockTables) {\n            if (\n                !(\n                    DriverUtils.isPostgresFamily(driver) ||\n                    driver.options.type === \"cockroachdb\"\n                )\n            ) {\n                throw new TypeORMError(\n                    \"Lock tables not supported in selected driver\",\n                )\n            }\n            if (this.expressionMap.lockTables.length < 1) {\n                throw new TypeORMError(\"lockTables cannot be an empty array\")\n            }\n            lockTablesClause = \" OF \" + this.expressionMap.lockTables.join(\", \")\n        }\n\n        let onLockExpression = \"\"\n        if (this.expressionMap.onLocked === \"nowait\") {\n            onLockExpression = \" NOWAIT\"\n        } else if (this.expressionMap.onLocked === \"skip_locked\") {\n            onLockExpression = \" SKIP LOCKED\"\n        }\n        switch (this.expressionMap.lockMode) {\n            case \"pessimistic_read\":\n                if (\n                    driver.options.type === \"mysql\" ||\n                    driver.options.type === \"aurora-mysql\"\n                ) {\n                    if (\n                        DriverUtils.isReleaseVersionOrGreater(driver, \"8.0.0\")\n                    ) {\n                        return (\n                            \" FOR SHARE\" + lockTablesClause + onLockExpression\n                        )\n                    } else {\n                        return \" LOCK IN SHARE MODE\"\n                    }\n                } else if (driver.options.type === \"mariadb\") {\n                    return \" LOCK IN SHARE MODE\"\n                } else if (DriverUtils.isPostgresFamily(driver)) {\n                    return \" FOR SHARE\" + lockTablesClause + onLockExpression\n                } else if (driver.options.type === \"oracle\") {\n                    return \" FOR UPDATE\"\n                } else if (driver.options.type === \"mssql\") {\n                    return \"\"\n                } else {\n                    throw new LockNotSupportedOnGivenDriverError()\n                }\n            case \"pessimistic_write\":\n                if (\n                    DriverUtils.isMySQLFamily(driver) ||\n                    driver.options.type === \"aurora-mysql\" ||\n                    driver.options.type === \"oracle\"\n                ) {\n                    return \" FOR UPDATE\" + onLockExpression\n                } else if (\n                    DriverUtils.isPostgresFamily(driver) ||\n                    driver.options.type === \"cockroachdb\"\n                ) {\n                    return \" FOR UPDATE\" + lockTablesClause + onLockExpression\n                } else if (driver.options.type === \"mssql\") {\n                    return \"\"\n                } else {\n                    throw new LockNotSupportedOnGivenDriverError()\n                }\n            case \"pessimistic_partial_write\":\n                if (DriverUtils.isPostgresFamily(driver)) {\n                    return \" FOR UPDATE\" + lockTablesClause + \" SKIP LOCKED\"\n                } else if (DriverUtils.isMySQLFamily(driver)) {\n                    return \" FOR UPDATE SKIP LOCKED\"\n                } else {\n                    throw new LockNotSupportedOnGivenDriverError()\n                }\n            case \"pessimistic_write_or_fail\":\n                if (\n                    DriverUtils.isPostgresFamily(driver) ||\n                    driver.options.type === \"cockroachdb\"\n                ) {\n                    return \" FOR UPDATE\" + lockTablesClause + \" NOWAIT\"\n                } else if (DriverUtils.isMySQLFamily(driver)) {\n                    return \" FOR UPDATE NOWAIT\"\n                } else {\n                    throw new LockNotSupportedOnGivenDriverError()\n                }\n            case \"for_no_key_update\":\n                if (\n                    DriverUtils.isPostgresFamily(driver) ||\n                    driver.options.type === \"cockroachdb\"\n                ) {\n                    return (\n                        \" FOR NO KEY UPDATE\" +\n                        lockTablesClause +\n                        onLockExpression\n                    )\n                } else {\n                    throw new LockNotSupportedOnGivenDriverError()\n                }\n            case \"for_key_share\":\n                if (DriverUtils.isPostgresFamily(driver)) {\n                    return (\n                        \" FOR KEY SHARE\" + lockTablesClause + onLockExpression\n                    )\n                } else {\n                    throw new LockNotSupportedOnGivenDriverError()\n                }\n            default:\n                return \"\"\n        }\n    }\n\n    /**\n     * Creates \"HAVING\" part of SQL query.\n     */\n    protected createHavingExpression() {\n        if (!this.expressionMap.havings || !this.expressionMap.havings.length)\n            return \"\"\n        const conditions = this.expressionMap.havings\n            .map((having, index) => {\n                switch (having.type) {\n                    case \"and\":\n                        return (\n                            (index > 0 ? \"AND \" : \"\") +\n                            this.replacePropertyNames(having.condition)\n                        )\n                    case \"or\":\n                        return (\n                            (index > 0 ? \"OR \" : \"\") +\n                            this.replacePropertyNames(having.condition)\n                        )\n                    default:\n                        return this.replacePropertyNames(having.condition)\n                }\n            })\n            .join(\" \")\n\n        if (!conditions.length) return \"\"\n        return \" HAVING \" + conditions\n    }\n\n    protected buildEscapedEntityColumnSelects(\n        aliasName: string,\n        metadata: EntityMetadata,\n    ): SelectQuery[] {\n        const hasMainAlias = this.expressionMap.selects.some(\n            (select) => select.selection === aliasName,\n        )\n\n        const columns: ColumnMetadata[] = []\n        if (hasMainAlias) {\n            columns.push(\n                ...metadata.columns.filter(\n                    (column) => column.isSelect === true,\n                ),\n            )\n        }\n        columns.push(\n            ...metadata.columns.filter((column) => {\n                return this.expressionMap.selects.some(\n                    (select) =>\n                        select.selection ===\n                        aliasName + \".\" + column.propertyPath,\n                )\n            }),\n        )\n\n        // if user used partial selection and did not select some primary columns which are required to be selected\n        // we select those primary columns and mark them as \"virtual\". Later virtual column values will be removed from final entity\n        // to make entity contain exactly what user selected\n        if (columns.length === 0)\n            // however not in the case when nothing (even partial) was selected from this target (for example joins without selection)\n            return []\n\n        const nonSelectedPrimaryColumns = this.expressionMap.queryEntity\n            ? metadata.primaryColumns.filter(\n                  (primaryColumn) => columns.indexOf(primaryColumn) === -1,\n              )\n            : []\n        const allColumns = [...columns, ...nonSelectedPrimaryColumns]\n        const finalSelects: SelectQuery[] = []\n\n        const escapedAliasName = this.escape(aliasName)\n        allColumns.forEach((column) => {\n            let selectionPath =\n                escapedAliasName + \".\" + this.escape(column.databaseName)\n\n            if (column.isVirtualProperty && column.query) {\n                selectionPath = `(${column.query(escapedAliasName)})`\n            }\n\n            if (\n                this.connection.driver.spatialTypes.indexOf(column.type) !== -1\n            ) {\n                if (\n                    DriverUtils.isMySQLFamily(this.connection.driver) ||\n                    this.connection.driver.options.type === \"aurora-mysql\"\n                ) {\n                    const useLegacy = (\n                        this.connection.driver as\n                            | MysqlDriver\n                            | AuroraMysqlDriver\n                    ).options.legacySpatialSupport\n                    const asText = useLegacy ? \"AsText\" : \"ST_AsText\"\n                    selectionPath = `${asText}(${selectionPath})`\n                }\n\n                if (DriverUtils.isPostgresFamily(this.connection.driver))\n                    if (column.precision) {\n                        // cast to JSON to trigger parsing in the driver\n                        selectionPath = `ST_AsGeoJSON(${selectionPath}, ${column.precision})::json`\n                    } else {\n                        selectionPath = `ST_AsGeoJSON(${selectionPath})::json`\n                    }\n                if (this.connection.driver.options.type === \"mssql\")\n                    selectionPath = `${selectionPath}.ToString()`\n            }\n\n            const selections = this.expressionMap.selects.filter(\n                (select) =>\n                    select.selection === aliasName + \".\" + column.propertyPath,\n            )\n            if (selections.length) {\n                selections.forEach((selection) => {\n                    finalSelects.push({\n                        selection: selectionPath,\n                        aliasName: selection.aliasName\n                            ? selection.aliasName\n                            : DriverUtils.buildAlias(\n                                  this.connection.driver,\n                                  undefined,\n                                  aliasName,\n                                  column.databaseName,\n                              ),\n                        // todo: need to keep in mind that custom selection.aliasName breaks hydrator. fix it later!\n                        virtual: selection.virtual,\n                    })\n                })\n            } else {\n                finalSelects.push({\n                    selection: selectionPath,\n                    aliasName: DriverUtils.buildAlias(\n                        this.connection.driver,\n                        undefined,\n                        aliasName,\n                        column.databaseName,\n                    ),\n                    // todo: need to keep in mind that custom selection.aliasName breaks hydrator. fix it later!\n                    virtual: hasMainAlias,\n                })\n            }\n        })\n        return finalSelects\n    }\n\n    protected findEntityColumnSelects(\n        aliasName: string,\n        metadata: EntityMetadata,\n    ): SelectQuery[] {\n        const mainSelect = this.expressionMap.selects.find(\n            (select) => select.selection === aliasName,\n        )\n        if (mainSelect) return [mainSelect]\n\n        return this.expressionMap.selects.filter((select) => {\n            return metadata.columns.some(\n                (column) =>\n                    select.selection === aliasName + \".\" + column.propertyPath,\n            )\n        })\n    }\n\n    private computeCountExpression() {\n        const mainAlias = this.expressionMap.mainAlias!.name // todo: will this work with \"fromTableName\"?\n        const metadata = this.expressionMap.mainAlias!.metadata\n\n        const primaryColumns = metadata.primaryColumns\n        const distinctAlias = this.escape(mainAlias)\n\n        // If we aren't doing anything that will create a join, we can use a simpler `COUNT` instead\n        // so we prevent poor query patterns in the most likely cases\n        if (\n            this.expressionMap.joinAttributes.length === 0 &&\n            this.expressionMap.relationIdAttributes.length === 0 &&\n            this.expressionMap.relationCountAttributes.length === 0\n        ) {\n            return \"COUNT(1)\"\n        }\n\n        // For everything else, we'll need to do some hackery to get the correct count values.\n\n        if (\n            this.connection.driver.options.type === \"cockroachdb\" ||\n            DriverUtils.isPostgresFamily(this.connection.driver)\n        ) {\n            // Postgres and CockroachDB can pass multiple parameters to the `DISTINCT` function\n            // https://www.postgresql.org/docs/9.5/sql-select.html#SQL-DISTINCT\n            return (\n                \"COUNT(DISTINCT(\" +\n                primaryColumns\n                    .map(\n                        (c) =>\n                            `${distinctAlias}.${this.escape(c.databaseName)}`,\n                    )\n                    .join(\", \") +\n                \"))\"\n            )\n        }\n\n        if (DriverUtils.isMySQLFamily(this.connection.driver)) {\n            // MySQL & MariaDB can pass multiple parameters to the `DISTINCT` language construct\n            // https://mariadb.com/kb/en/count-distinct/\n            return (\n                \"COUNT(DISTINCT \" +\n                primaryColumns\n                    .map(\n                        (c) =>\n                            `${distinctAlias}.${this.escape(c.databaseName)}`,\n                    )\n                    .join(\", \") +\n                \")\"\n            )\n        }\n\n        if (this.connection.driver.options.type === \"mssql\") {\n            // SQL Server has gotta be different from everyone else.  They don't support\n            // distinct counting multiple columns & they don't have the same operator\n            // characteristic for concatenating, so we gotta use the `CONCAT` function.\n            // However, If it's exactly 1 column we can omit the `CONCAT` for better performance.\n\n            const columnsExpression = primaryColumns\n                .map(\n                    (primaryColumn) =>\n                        `${distinctAlias}.${this.escape(\n                            primaryColumn.databaseName,\n                        )}`,\n                )\n                .join(\", '|;|', \")\n\n            if (primaryColumns.length === 1) {\n                return `COUNT(DISTINCT(${columnsExpression}))`\n            }\n\n            return `COUNT(DISTINCT(CONCAT(${columnsExpression})))`\n        }\n\n        if (this.connection.driver.options.type === \"spanner\") {\n            // spanner also has gotta be different from everyone else.\n            // they do not support concatenation of different column types without casting them to string\n\n            if (primaryColumns.length === 1) {\n                return `COUNT(DISTINCT(${distinctAlias}.${this.escape(\n                    primaryColumns[0].databaseName,\n                )}))`\n            }\n\n            const columnsExpression = primaryColumns\n                .map(\n                    (primaryColumn) =>\n                        `CAST(${distinctAlias}.${this.escape(\n                            primaryColumn.databaseName,\n                        )} AS STRING)`,\n                )\n                .join(\", '|;|', \")\n            return `COUNT(DISTINCT(CONCAT(${columnsExpression})))`\n        }\n\n        // If all else fails, fall back to a `COUNT` and `DISTINCT` across all the primary columns concatenated.\n        // Per the SQL spec, this is the canonical string concatenation mechanism which is most\n        // likely to work across servers implementing the SQL standard.\n\n        // Please note, if there is only one primary column that the concatenation does not occur in this\n        // query and the query is a standard `COUNT DISTINCT` in that case.\n\n        return (\n            `COUNT(DISTINCT(` +\n            primaryColumns\n                .map((c) => `${distinctAlias}.${this.escape(c.databaseName)}`)\n                .join(\" || '|;|' || \") +\n            \"))\"\n        )\n    }\n\n    protected async executeCountQuery(\n        queryRunner: QueryRunner,\n    ): Promise<number> {\n        const countSql = this.computeCountExpression()\n\n        const results = await this.clone()\n            .orderBy()\n            .groupBy()\n            .offset(undefined)\n            .limit(undefined)\n            .skip(undefined)\n            .take(undefined)\n            .select(countSql, \"cnt\")\n            .setOption(\"disable-global-order\")\n            .loadRawResults(queryRunner)\n\n        if (!results || !results[0] || !results[0][\"cnt\"]) return 0\n\n        return parseInt(results[0][\"cnt\"])\n    }\n\n    protected async executeExistsQuery(\n        queryRunner: QueryRunner,\n    ): Promise<boolean> {\n        const results = await this.connection\n            .createQueryBuilder()\n            .fromDummy()\n            .select(\"1\", \"row_exists\")\n            .whereExists(this)\n            .limit(1)\n            .loadRawResults(queryRunner)\n\n        return results.length > 0\n    }\n\n    protected applyFindOptions() {\n        // todo: convert relations: string[] to object map to simplify code\n        // todo: same with selects\n\n        if (this.expressionMap.mainAlias!.metadata) {\n            if (this.findOptions.relationLoadStrategy) {\n                this.expressionMap.relationLoadStrategy =\n                    this.findOptions.relationLoadStrategy\n            }\n\n            if (this.findOptions.comment) {\n                this.comment(this.findOptions.comment)\n            }\n\n            if (this.findOptions.withDeleted) {\n                this.withDeleted()\n            }\n\n            if (this.findOptions.select) {\n                const select = Array.isArray(this.findOptions.select)\n                    ? OrmUtils.propertyPathsToTruthyObject(\n                          this.findOptions.select as string[],\n                      )\n                    : this.findOptions.select\n\n                this.buildSelect(\n                    select,\n                    this.expressionMap.mainAlias!.metadata,\n                    this.expressionMap.mainAlias!.name,\n                )\n            }\n\n            if (this.selects.length) {\n                this.select(this.selects)\n            }\n\n            this.selects = []\n            if (this.findOptions.relations) {\n                const relations = Array.isArray(this.findOptions.relations)\n                    ? OrmUtils.propertyPathsToTruthyObject(\n                          this.findOptions.relations,\n                      )\n                    : this.findOptions.relations\n\n                this.buildRelations(\n                    relations,\n                    typeof this.findOptions.select === \"object\"\n                        ? (this.findOptions.select as FindOptionsSelect<any>)\n                        : undefined,\n                    this.expressionMap.mainAlias!.metadata,\n                    this.expressionMap.mainAlias!.name,\n                )\n                if (\n                    this.findOptions.loadEagerRelations !== false &&\n                    this.expressionMap.relationLoadStrategy === \"join\"\n                ) {\n                    this.buildEagerRelations(\n                        relations,\n                        typeof this.findOptions.select === \"object\"\n                            ? (this.findOptions\n                                  .select as FindOptionsSelect<any>)\n                            : undefined,\n                        this.expressionMap.mainAlias!.metadata,\n                        this.expressionMap.mainAlias!.name,\n                    )\n                }\n            }\n            if (this.selects.length) {\n                this.addSelect(this.selects)\n            }\n\n            if (this.findOptions.where) {\n                this.conditions = this.buildWhere(\n                    this.findOptions.where,\n                    this.expressionMap.mainAlias!.metadata,\n                    this.expressionMap.mainAlias!.name,\n                )\n\n                if (this.conditions.length)\n                    this.andWhere(\n                        this.conditions.substr(0, 1) !== \"(\"\n                            ? \"(\" + this.conditions + \")\"\n                            : this.conditions,\n                    ) // temporary and where and braces\n            }\n\n            if (this.findOptions.order) {\n                this.buildOrder(\n                    this.findOptions.order,\n                    this.expressionMap.mainAlias!.metadata,\n                    this.expressionMap.mainAlias!.name,\n                )\n            }\n\n            // apply joins\n            if (this.joins.length) {\n                this.joins.forEach((join) => {\n                    if (join.select && !join.selection) {\n                        // if (join.selection) {\n                        //\n                        // } else {\n                        if (join.type === \"inner\") {\n                            this.innerJoinAndSelect(\n                                `${join.parentAlias}.${join.relationMetadata.propertyPath}`,\n                                join.alias,\n                            )\n                        } else {\n                            this.leftJoinAndSelect(\n                                `${join.parentAlias}.${join.relationMetadata.propertyPath}`,\n                                join.alias,\n                            )\n                        }\n                        // }\n                    } else {\n                        if (join.type === \"inner\") {\n                            this.innerJoin(\n                                `${join.parentAlias}.${join.relationMetadata.propertyPath}`,\n                                join.alias,\n                            )\n                        } else {\n                            this.leftJoin(\n                                `${join.parentAlias}.${join.relationMetadata.propertyPath}`,\n                                join.alias,\n                            )\n                        }\n                    }\n\n                    // if (join.select) {\n                    //     if (this.findOptions.loadEagerRelations !== false) {\n                    //         FindOptionsUtils.joinEagerRelations(\n                    //             this,\n                    //             join.alias,\n                    //             join.relationMetadata.inverseEntityMetadata\n                    //         );\n                    //     }\n                    // }\n                })\n            }\n\n            // if (this.conditions.length) {\n            //     this.where(this.conditions.join(\" AND \"));\n            // }\n\n            // apply offset\n            if (this.findOptions.skip !== undefined) {\n                // if (this.findOptions.options && this.findOptions.options.pagination === false) {\n                //     this.offset(this.findOptions.skip);\n                // } else {\n                this.skip(this.findOptions.skip)\n                // }\n            }\n\n            // apply limit\n            if (this.findOptions.take !== undefined) {\n                // if (this.findOptions.options && this.findOptions.options.pagination === false) {\n                //     this.limit(this.findOptions.take);\n                // } else {\n                this.take(this.findOptions.take)\n                // }\n            }\n\n            // apply caching options\n            if (typeof this.findOptions.cache === \"number\") {\n                this.cache(this.findOptions.cache)\n            } else if (typeof this.findOptions.cache === \"boolean\") {\n                this.cache(this.findOptions.cache)\n            } else if (typeof this.findOptions.cache === \"object\") {\n                this.cache(\n                    this.findOptions.cache.id,\n                    this.findOptions.cache.milliseconds,\n                )\n            }\n\n            if (this.findOptions.join) {\n                if (this.findOptions.join.leftJoin)\n                    Object.keys(this.findOptions.join.leftJoin).forEach(\n                        (key) => {\n                            this.leftJoin(\n                                this.findOptions.join!.leftJoin![key],\n                                key,\n                            )\n                        },\n                    )\n\n                if (this.findOptions.join.innerJoin)\n                    Object.keys(this.findOptions.join.innerJoin).forEach(\n                        (key) => {\n                            this.innerJoin(\n                                this.findOptions.join!.innerJoin![key],\n                                key,\n                            )\n                        },\n                    )\n\n                if (this.findOptions.join.leftJoinAndSelect)\n                    Object.keys(\n                        this.findOptions.join.leftJoinAndSelect,\n                    ).forEach((key) => {\n                        this.leftJoinAndSelect(\n                            this.findOptions.join!.leftJoinAndSelect![key],\n                            key,\n                        )\n                    })\n\n                if (this.findOptions.join.innerJoinAndSelect)\n                    Object.keys(\n                        this.findOptions.join.innerJoinAndSelect,\n                    ).forEach((key) => {\n                        this.innerJoinAndSelect(\n                            this.findOptions.join!.innerJoinAndSelect![key],\n                            key,\n                        )\n                    })\n            }\n\n            if (this.findOptions.lock) {\n                if (this.findOptions.lock.mode === \"optimistic\") {\n                    this.setLock(\n                        this.findOptions.lock.mode,\n                        this.findOptions.lock.version,\n                    )\n                } else if (\n                    this.findOptions.lock.mode === \"pessimistic_read\" ||\n                    this.findOptions.lock.mode === \"pessimistic_write\" ||\n                    this.findOptions.lock.mode === \"dirty_read\" ||\n                    this.findOptions.lock.mode ===\n                        \"pessimistic_partial_write\" ||\n                    this.findOptions.lock.mode ===\n                        \"pessimistic_write_or_fail\" ||\n                    this.findOptions.lock.mode === \"for_no_key_update\" ||\n                    this.findOptions.lock.mode === \"for_key_share\"\n                ) {\n                    const tableNames = this.findOptions.lock.tables\n                        ? this.findOptions.lock.tables.map((table) => {\n                              const tableAlias =\n                                  this.expressionMap.aliases.find((alias) => {\n                                      return (\n                                          alias.metadata\n                                              .tableNameWithoutPrefix === table\n                                      )\n                                  })\n                              if (!tableAlias) {\n                                  throw new TypeORMError(\n                                      `\"${table}\" is not part of this query`,\n                                  )\n                              }\n                              return this.escape(tableAlias.name)\n                          })\n                        : undefined\n                    this.setLock(\n                        this.findOptions.lock.mode,\n                        undefined,\n                        tableNames,\n                    )\n\n                    if (this.findOptions.lock.onLocked) {\n                        this.setOnLocked(this.findOptions.lock.onLocked)\n                    }\n                }\n            }\n\n            if (this.findOptions.loadRelationIds === true) {\n                this.loadAllRelationIds()\n            } else if (typeof this.findOptions.loadRelationIds === \"object\") {\n                this.loadAllRelationIds(this.findOptions.loadRelationIds as any)\n            }\n\n            if (this.findOptions.loadEagerRelations !== false) {\n                FindOptionsUtils.joinEagerRelations(\n                    this,\n                    this.expressionMap.mainAlias!.name,\n                    this.expressionMap.mainAlias!.metadata,\n                )\n            }\n\n            if (this.findOptions.transaction === true) {\n                this.expressionMap.useTransaction = true\n            }\n\n            // if (this.orderBys.length) {\n            //     this.orderBys.forEach(orderBy => {\n            //         this.addOrderBy(orderBy.alias, orderBy.direction, orderBy.nulls);\n            //     });\n            // }\n\n            // todo\n            // if (this.options.options && this.options.options.eagerRelations) {\n            //     this.queryBuilder\n            // }\n\n            // todo\n            // if (this.findOptions.options && this.findOptions.listeners === false) {\n            //     this.callListeners(false);\n            // }\n        }\n    }\n\n    public concatRelationMetadata(relationMetadata: RelationMetadata) {\n        this.relationMetadatas.push(relationMetadata)\n    }\n\n    /**\n     * Executes sql generated by query builder and returns object with raw results and entities created from them.\n     */\n    protected async executeEntitiesAndRawResults(\n        queryRunner: QueryRunner,\n    ): Promise<{ entities: Entity[]; raw: any[] }> {\n        if (!this.expressionMap.mainAlias)\n            throw new TypeORMError(\n                `Alias is not set. Use \"from\" method to set an alias.`,\n            )\n\n        if (\n            (this.expressionMap.lockMode === \"pessimistic_read\" ||\n                this.expressionMap.lockMode === \"pessimistic_write\" ||\n                this.expressionMap.lockMode === \"pessimistic_partial_write\" ||\n                this.expressionMap.lockMode === \"pessimistic_write_or_fail\" ||\n                this.expressionMap.lockMode === \"for_no_key_update\" ||\n                this.expressionMap.lockMode === \"for_key_share\") &&\n            !queryRunner.isTransactionActive\n        )\n            throw new PessimisticLockTransactionRequiredError()\n\n        if (this.expressionMap.lockMode === \"optimistic\") {\n            const metadata = this.expressionMap.mainAlias.metadata\n            if (!metadata.versionColumn && !metadata.updateDateColumn)\n                throw new NoVersionOrUpdateDateColumnError(metadata.name)\n        }\n\n        const relationIdLoader = new RelationIdLoader(\n            this.connection,\n            queryRunner,\n            this.expressionMap.relationIdAttributes,\n        )\n        const relationCountLoader = new RelationCountLoader(\n            this.connection,\n            queryRunner,\n            this.expressionMap.relationCountAttributes,\n        )\n        const relationIdMetadataTransformer =\n            new RelationIdMetadataToAttributeTransformer(this.expressionMap)\n        relationIdMetadataTransformer.transform()\n        const relationCountMetadataTransformer =\n            new RelationCountMetadataToAttributeTransformer(this.expressionMap)\n        relationCountMetadataTransformer.transform()\n\n        let rawResults: any[] = [],\n            entities: any[] = []\n\n        // for pagination enabled (e.g. skip and take) its much more complicated - its a special process\n        // where we make two queries to find the data we need\n        // first query find ids in skip and take range\n        // and second query loads the actual data in given ids range\n        if (\n            (this.expressionMap.skip || this.expressionMap.take) &&\n            this.expressionMap.joinAttributes.length > 0\n        ) {\n            // we are skipping order by here because its not working in subqueries anyway\n            // to make order by working we need to apply it on a distinct query\n            const [selects, orderBys] =\n                this.createOrderByCombinedWithSelectExpression(\"distinctAlias\")\n            const metadata = this.expressionMap.mainAlias.metadata\n            const mainAliasName = this.expressionMap.mainAlias.name\n\n            const querySelects = metadata.primaryColumns.map(\n                (primaryColumn) => {\n                    const distinctAlias = this.escape(\"distinctAlias\")\n                    const columnAlias = this.escape(\n                        DriverUtils.buildAlias(\n                            this.connection.driver,\n                            undefined,\n                            mainAliasName,\n                            primaryColumn.databaseName,\n                        ),\n                    )\n                    if (!orderBys[columnAlias])\n                        // make sure we aren't overriding user-defined order in inverse direction\n                        orderBys[columnAlias] = \"ASC\"\n\n                    const alias = DriverUtils.buildAlias(\n                        this.connection.driver,\n                        undefined,\n                        \"ids_\" + mainAliasName,\n                        primaryColumn.databaseName,\n                    )\n\n                    return `${distinctAlias}.${columnAlias} AS ${this.escape(\n                        alias,\n                    )}`\n                },\n            )\n\n            const originalQuery = this.clone()\n\n            // preserve original timeTravel value since we set it to \"false\" in subquery\n            const originalQueryTimeTravel =\n                originalQuery.expressionMap.timeTravel\n\n            rawResults = await new SelectQueryBuilder(\n                this.connection,\n                queryRunner,\n            )\n                .select(`DISTINCT ${querySelects.join(\", \")}`)\n                .addSelect(selects)\n                .from(\n                    `(${originalQuery\n                        .orderBy()\n                        .timeTravelQuery(false) // set it to \"false\" since time travel clause must appear at the very end and applies to the entire SELECT clause.\n                        .getQuery()})`,\n                    \"distinctAlias\",\n                )\n                .timeTravelQuery(originalQueryTimeTravel)\n                .offset(this.expressionMap.skip)\n                .limit(this.expressionMap.take)\n                .orderBy(orderBys)\n                .cache(\n                    this.expressionMap.cache && this.expressionMap.cacheId\n                        ? `${this.expressionMap.cacheId}-pagination`\n                        : this.expressionMap.cache,\n                    this.expressionMap.cacheDuration,\n                )\n                .setParameters(this.getParameters())\n                .setNativeParameters(this.expressionMap.nativeParameters)\n                .getRawMany()\n\n            if (rawResults.length > 0) {\n                let condition = \"\"\n                const parameters: ObjectLiteral = {}\n                if (metadata.hasMultiplePrimaryKeys) {\n                    condition = rawResults\n                        .map((result, index) => {\n                            return metadata.primaryColumns\n                                .map((primaryColumn) => {\n                                    const paramKey = `orm_distinct_ids_${index}_${primaryColumn.databaseName}`\n                                    const paramKeyResult =\n                                        DriverUtils.buildAlias(\n                                            this.connection.driver,\n                                            undefined,\n                                            \"ids_\" + mainAliasName,\n                                            primaryColumn.databaseName,\n                                        )\n                                    parameters[paramKey] =\n                                        result[paramKeyResult]\n                                    return `${mainAliasName}.${primaryColumn.propertyPath}=:${paramKey}`\n                                })\n                                .join(\" AND \")\n                        })\n                        .join(\" OR \")\n                } else {\n                    const alias = DriverUtils.buildAlias(\n                        this.connection.driver,\n                        undefined,\n                        \"ids_\" + mainAliasName,\n                        metadata.primaryColumns[0].databaseName,\n                    )\n\n                    const ids = rawResults.map((result) => result[alias])\n                    const areAllNumbers = ids.every(\n                        (id: any) => typeof id === \"number\",\n                    )\n                    if (areAllNumbers) {\n                        // fixes #190. if all numbers then its safe to perform query without parameter\n                        condition = `${mainAliasName}.${\n                            metadata.primaryColumns[0].propertyPath\n                        } IN (${ids.join(\", \")})`\n                    } else {\n                        parameters[\"orm_distinct_ids\"] = ids\n                        condition =\n                            mainAliasName +\n                            \".\" +\n                            metadata.primaryColumns[0].propertyPath +\n                            \" IN (:...orm_distinct_ids)\"\n                    }\n                }\n                rawResults = await this.clone()\n                    .mergeExpressionMap({\n                        extraAppendedAndWhereCondition: condition,\n                    })\n                    .setParameters(parameters)\n                    .loadRawResults(queryRunner)\n            }\n        } else {\n            rawResults = await this.loadRawResults(queryRunner)\n        }\n\n        if (rawResults.length > 0) {\n            // transform raw results into entities\n            const rawRelationIdResults = await relationIdLoader.load(rawResults)\n            const rawRelationCountResults = await relationCountLoader.load(\n                rawResults,\n            )\n            const transformer = new RawSqlResultsToEntityTransformer(\n                this.expressionMap,\n                this.connection.driver,\n                rawRelationIdResults,\n                rawRelationCountResults,\n                this.queryRunner,\n            )\n            entities = transformer.transform(\n                rawResults,\n                this.expressionMap.mainAlias!,\n            )\n\n            // broadcast all \"after load\" events\n            if (\n                this.expressionMap.callListeners === true &&\n                this.expressionMap.mainAlias.hasMetadata\n            ) {\n                await queryRunner.broadcaster.broadcast(\n                    \"Load\",\n                    this.expressionMap.mainAlias.metadata,\n                    entities,\n                )\n            }\n        }\n\n        if (this.expressionMap.relationLoadStrategy === \"query\") {\n            const queryStrategyRelationIdLoader =\n                new QueryStrategyRelationIdLoader(this.connection, queryRunner)\n\n            await Promise.all(\n                this.relationMetadatas.map(async (relation) => {\n                    const relationTarget = relation.inverseEntityMetadata.target\n                    const relationAlias =\n                        relation.inverseEntityMetadata.targetName\n\n                    const select = Array.isArray(this.findOptions.select)\n                        ? OrmUtils.propertyPathsToTruthyObject(\n                              this.findOptions.select as string[],\n                          )\n                        : this.findOptions.select\n                    const relations = Array.isArray(this.findOptions.relations)\n                        ? OrmUtils.propertyPathsToTruthyObject(\n                              this.findOptions.relations,\n                          )\n                        : this.findOptions.relations\n\n                    const queryBuilder = this.createQueryBuilder(queryRunner)\n                        .select(relationAlias)\n                        .from(relationTarget, relationAlias)\n                        .setFindOptions({\n                            select: select\n                                ? OrmUtils.deepValue(\n                                      select,\n                                      relation.propertyPath,\n                                  )\n                                : undefined,\n                            order: this.findOptions.order\n                                ? OrmUtils.deepValue(\n                                      this.findOptions.order,\n                                      relation.propertyPath,\n                                  )\n                                : undefined,\n                            relations: relations\n                                ? OrmUtils.deepValue(\n                                      relations,\n                                      relation.propertyPath,\n                                  )\n                                : undefined,\n                            withDeleted: this.findOptions.withDeleted,\n                            relationLoadStrategy:\n                                this.findOptions.relationLoadStrategy,\n                        })\n                    if (entities.length > 0) {\n                        const relatedEntityGroups: any[] =\n                            await queryStrategyRelationIdLoader.loadManyToManyRelationIdsAndGroup(\n                                relation,\n                                entities,\n                                undefined,\n                                queryBuilder,\n                            )\n                        entities.forEach((entity) => {\n                            const relatedEntityGroup = relatedEntityGroups.find(\n                                (group) => group.entity === entity,\n                            )\n                            if (relatedEntityGroup) {\n                                const value =\n                                    relatedEntityGroup.related === undefined\n                                        ? null\n                                        : relatedEntityGroup.related\n                                relation.setEntityValue(entity, value)\n                            }\n                        })\n                    }\n                }),\n            )\n        }\n\n        return {\n            raw: rawResults,\n            entities: entities,\n        }\n    }\n\n    protected createOrderByCombinedWithSelectExpression(\n        parentAlias: string,\n    ): [string, OrderByCondition] {\n        // if table has a default order then apply it\n        const orderBys = this.expressionMap.allOrderBys\n        const selectString = Object.keys(orderBys)\n            .map((orderCriteria) => {\n                if (orderCriteria.indexOf(\".\") !== -1) {\n                    const criteriaParts = orderCriteria.split(\".\")\n                    const aliasName = criteriaParts[0]\n                    const propertyPath = criteriaParts.slice(1).join(\".\")\n                    const alias = this.expressionMap.findAliasByName(aliasName)\n                    const column =\n                        alias.metadata.findColumnWithPropertyPath(propertyPath)\n                    return (\n                        this.escape(parentAlias) +\n                        \".\" +\n                        this.escape(\n                            DriverUtils.buildAlias(\n                                this.connection.driver,\n                                undefined,\n                                aliasName,\n                                column!.databaseName,\n                            ),\n                        )\n                    )\n                } else {\n                    if (\n                        this.expressionMap.selects.find(\n                            (select) =>\n                                select.selection === orderCriteria ||\n                                select.aliasName === orderCriteria,\n                        )\n                    )\n                        return (\n                            this.escape(parentAlias) +\n                            \".\" +\n                            this.escape(orderCriteria)\n                        )\n\n                    return \"\"\n                }\n            })\n            .join(\", \")\n\n        const orderByObject: OrderByCondition = {}\n        Object.keys(orderBys).forEach((orderCriteria) => {\n            if (orderCriteria.indexOf(\".\") !== -1) {\n                const criteriaParts = orderCriteria.split(\".\")\n                const aliasName = criteriaParts[0]\n                const propertyPath = criteriaParts.slice(1).join(\".\")\n                const alias = this.expressionMap.findAliasByName(aliasName)\n                const column =\n                    alias.metadata.findColumnWithPropertyPath(propertyPath)\n                orderByObject[\n                    this.escape(parentAlias) +\n                        \".\" +\n                        this.escape(\n                            DriverUtils.buildAlias(\n                                this.connection.driver,\n                                undefined,\n                                aliasName,\n                                column!.databaseName,\n                            ),\n                        )\n                ] = orderBys[orderCriteria]\n            } else {\n                if (\n                    this.expressionMap.selects.find(\n                        (select) =>\n                            select.selection === orderCriteria ||\n                            select.aliasName === orderCriteria,\n                    )\n                ) {\n                    orderByObject[\n                        this.escape(parentAlias) +\n                            \".\" +\n                            this.escape(orderCriteria)\n                    ] = orderBys[orderCriteria]\n                } else {\n                    orderByObject[orderCriteria] = orderBys[orderCriteria]\n                }\n            }\n        })\n\n        return [selectString, orderByObject]\n    }\n\n    /**\n     * Loads raw results from the database.\n     */\n    protected async loadRawResults(queryRunner: QueryRunner) {\n        const [sql, parameters] = this.getQueryAndParameters()\n        const queryId =\n            sql +\n            \" -- PARAMETERS: \" +\n            JSON.stringify(parameters, (_, value) =>\n                typeof value === \"bigint\" ? value.toString() : value,\n            )\n        const cacheOptions =\n            typeof this.connection.options.cache === \"object\"\n                ? this.connection.options.cache\n                : {}\n        let savedQueryResultCacheOptions: QueryResultCacheOptions | undefined =\n            undefined\n        const isCachingEnabled =\n            // Caching is enabled globally and isn't disabled locally.\n            (cacheOptions.alwaysEnabled &&\n                this.expressionMap.cache !== false) ||\n            // ...or it's enabled locally explicitly.\n            this.expressionMap.cache === true\n        let cacheError = false\n        if (this.connection.queryResultCache && isCachingEnabled) {\n            try {\n                savedQueryResultCacheOptions =\n                    await this.connection.queryResultCache.getFromCache(\n                        {\n                            identifier: this.expressionMap.cacheId,\n                            query: queryId,\n                            duration:\n                                this.expressionMap.cacheDuration ||\n                                cacheOptions.duration ||\n                                1000,\n                        },\n                        queryRunner,\n                    )\n                if (\n                    savedQueryResultCacheOptions &&\n                    !this.connection.queryResultCache.isExpired(\n                        savedQueryResultCacheOptions,\n                    )\n                ) {\n                    return JSON.parse(savedQueryResultCacheOptions.result)\n                }\n            } catch (error) {\n                if (!cacheOptions.ignoreErrors) {\n                    throw error\n                }\n                cacheError = true\n            }\n        }\n\n        const results = await queryRunner.query(sql, parameters, true)\n\n        if (\n            !cacheError &&\n            this.connection.queryResultCache &&\n            isCachingEnabled\n        ) {\n            try {\n                await this.connection.queryResultCache.storeInCache(\n                    {\n                        identifier: this.expressionMap.cacheId,\n                        query: queryId,\n                        time: Date.now(),\n                        duration:\n                            this.expressionMap.cacheDuration ||\n                            cacheOptions.duration ||\n                            1000,\n                        result: JSON.stringify(results.records),\n                    },\n                    savedQueryResultCacheOptions,\n                    queryRunner,\n                )\n            } catch (error) {\n                if (!cacheOptions.ignoreErrors) {\n                    throw error\n                }\n            }\n        }\n\n        return results.records\n    }\n\n    /**\n     * Merges into expression map given expression map properties.\n     */\n    protected mergeExpressionMap(\n        expressionMap: Partial<QueryExpressionMap>,\n    ): this {\n        ObjectUtils.assign(this.expressionMap, expressionMap)\n        return this\n    }\n\n    /**\n     * Normalizes a give number - converts to int if possible.\n     */\n    protected normalizeNumber(num: any) {\n        if (typeof num === \"number\" || num === undefined || num === null)\n            return num\n\n        return Number(num)\n    }\n\n    /**\n     * Creates a query builder used to execute sql queries inside this query builder.\n     */\n    protected obtainQueryRunner() {\n        return (\n            this.queryRunner ||\n            this.connection.createQueryRunner(\n                this.connection.defaultReplicationModeForReads(),\n            )\n        )\n    }\n\n    protected buildSelect(\n        select: FindOptionsSelect<any>,\n        metadata: EntityMetadata,\n        alias: string,\n        embedPrefix?: string,\n    ) {\n        for (const key in select) {\n            if (select[key] === undefined || select[key] === false) continue\n\n            const propertyPath = embedPrefix ? embedPrefix + \".\" + key : key\n            const column =\n                metadata.findColumnWithPropertyPathStrict(propertyPath)\n            const embed = metadata.findEmbeddedWithPropertyPath(propertyPath)\n            const relation = metadata.findRelationWithPropertyPath(propertyPath)\n\n            if (!embed && !column && !relation)\n                throw new EntityPropertyNotFoundError(propertyPath, metadata)\n\n            if (column) {\n                this.selects.push(alias + \".\" + propertyPath)\n                // this.addSelect(alias + \".\" + propertyPath);\n            } else if (embed) {\n                this.buildSelect(\n                    select[key] as FindOptionsSelect<any>,\n                    metadata,\n                    alias,\n                    propertyPath,\n                )\n\n                // } else if (relation) {\n                //     const joinAlias = alias + \"_\" + relation.propertyName;\n                //     const existJoin = this.joins.find(join => join.alias === joinAlias);\n                //     if (!existJoin) {\n                //         this.joins.push({\n                //             type: \"left\",\n                //             select: false,\n                //             alias: joinAlias,\n                //             parentAlias: alias,\n                //             relationMetadata: relation\n                //         });\n                //     }\n                //     this.buildOrder(select[key] as FindOptionsOrder<any>, relation.inverseEntityMetadata, joinAlias);\n            }\n        }\n    }\n\n    protected buildRelations(\n        relations: FindOptionsRelations<any>,\n        selection: FindOptionsSelect<any> | undefined,\n        metadata: EntityMetadata,\n        alias: string,\n        embedPrefix?: string,\n    ) {\n        if (!relations) return\n\n        Object.keys(relations).forEach((relationName) => {\n            const relationValue = (relations as any)[relationName]\n            const propertyPath = embedPrefix\n                ? embedPrefix + \".\" + relationName\n                : relationName\n            const embed = metadata.findEmbeddedWithPropertyPath(propertyPath)\n            const relation = metadata.findRelationWithPropertyPath(propertyPath)\n            if (!embed && !relation)\n                throw new EntityPropertyNotFoundError(propertyPath, metadata)\n\n            if (embed) {\n                this.buildRelations(\n                    relationValue,\n                    typeof selection === \"object\"\n                        ? OrmUtils.deepValue(selection, embed.propertyPath)\n                        : undefined,\n                    metadata,\n                    alias,\n                    propertyPath,\n                )\n            } else if (relation) {\n                let joinAlias = alias + \"_\" + propertyPath.replace(\".\", \"_\")\n                joinAlias = DriverUtils.buildAlias(\n                    this.connection.driver,\n                    { joiner: \"__\" },\n                    alias,\n                    joinAlias,\n                )\n                if (\n                    relationValue === true ||\n                    typeof relationValue === \"object\"\n                ) {\n                    if (this.expressionMap.relationLoadStrategy === \"query\") {\n                        this.concatRelationMetadata(relation)\n                    } else {\n                        // join\n                        this.joins.push({\n                            type: \"left\",\n                            select: true,\n                            selection:\n                                selection &&\n                                typeof selection[relationName] === \"object\"\n                                    ? (selection[\n                                          relationName\n                                      ] as FindOptionsSelect<any>)\n                                    : undefined,\n                            alias: joinAlias,\n                            parentAlias: alias,\n                            relationMetadata: relation,\n                        })\n\n                        if (\n                            selection &&\n                            typeof selection[relationName] === \"object\"\n                        ) {\n                            this.buildSelect(\n                                selection[\n                                    relationName\n                                ] as FindOptionsSelect<any>,\n                                relation.inverseEntityMetadata,\n                                joinAlias,\n                            )\n                        }\n                    }\n                }\n\n                if (\n                    typeof relationValue === \"object\" &&\n                    this.expressionMap.relationLoadStrategy === \"join\"\n                ) {\n                    this.buildRelations(\n                        relationValue,\n                        typeof selection === \"object\"\n                            ? OrmUtils.deepValue(\n                                  selection,\n                                  relation.propertyPath,\n                              )\n                            : undefined,\n                        relation.inverseEntityMetadata,\n                        joinAlias,\n                        undefined,\n                    )\n                }\n            }\n        })\n    }\n\n    protected buildEagerRelations(\n        relations: FindOptionsRelations<any>,\n        selection: FindOptionsSelect<any> | undefined,\n        metadata: EntityMetadata,\n        alias: string,\n        embedPrefix?: string,\n    ) {\n        if (!relations) return\n\n        Object.keys(relations).forEach((relationName) => {\n            const relationValue = (relations as any)[relationName]\n            const propertyPath = embedPrefix\n                ? embedPrefix + \".\" + relationName\n                : relationName\n            const embed = metadata.findEmbeddedWithPropertyPath(propertyPath)\n            const relation = metadata.findRelationWithPropertyPath(propertyPath)\n            if (!embed && !relation)\n                throw new EntityPropertyNotFoundError(propertyPath, metadata)\n\n            if (embed) {\n                this.buildEagerRelations(\n                    relationValue,\n                    typeof selection === \"object\"\n                        ? OrmUtils.deepValue(selection, embed.propertyPath)\n                        : undefined,\n                    metadata,\n                    alias,\n                    propertyPath,\n                )\n            } else if (relation) {\n                let joinAlias = alias + \"_\" + propertyPath.replace(\".\", \"_\")\n                joinAlias = DriverUtils.buildAlias(\n                    this.connection.driver,\n                    { joiner: \"__\" },\n                    alias,\n                    joinAlias,\n                )\n\n                if (\n                    relationValue === true ||\n                    typeof relationValue === \"object\"\n                ) {\n                    relation.inverseEntityMetadata.eagerRelations.forEach(\n                        (eagerRelation) => {\n                            let eagerRelationJoinAlias =\n                                joinAlias +\n                                \"_\" +\n                                eagerRelation.propertyPath.replace(\".\", \"_\")\n                            eagerRelationJoinAlias = DriverUtils.buildAlias(\n                                this.connection.driver,\n                                { joiner: \"__\" },\n                                joinAlias,\n                                eagerRelationJoinAlias,\n                            )\n\n                            const existJoin = this.joins.find(\n                                (join) => join.alias === eagerRelationJoinAlias,\n                            )\n                            if (!existJoin) {\n                                this.joins.push({\n                                    type: \"left\",\n                                    select: true,\n                                    alias: eagerRelationJoinAlias,\n                                    parentAlias: joinAlias,\n                                    selection: undefined,\n                                    relationMetadata: eagerRelation,\n                                })\n                            }\n\n                            if (\n                                selection &&\n                                typeof selection[relationName] === \"object\"\n                            ) {\n                                this.buildSelect(\n                                    selection[\n                                        relationName\n                                    ] as FindOptionsSelect<any>,\n                                    relation.inverseEntityMetadata,\n                                    joinAlias,\n                                )\n                            }\n                        },\n                    )\n                }\n\n                if (typeof relationValue === \"object\") {\n                    this.buildEagerRelations(\n                        relationValue,\n                        typeof selection === \"object\"\n                            ? OrmUtils.deepValue(\n                                  selection,\n                                  relation.propertyPath,\n                              )\n                            : undefined,\n                        relation.inverseEntityMetadata,\n                        joinAlias,\n                        undefined,\n                    )\n                }\n            }\n        })\n    }\n\n    protected buildOrder(\n        order: FindOptionsOrder<any>,\n        metadata: EntityMetadata,\n        alias: string,\n        embedPrefix?: string,\n    ) {\n        for (const key in order) {\n            if (order[key] === undefined) continue\n\n            const propertyPath = embedPrefix ? embedPrefix + \".\" + key : key\n            const column =\n                metadata.findColumnWithPropertyPathStrict(propertyPath)\n            const embed = metadata.findEmbeddedWithPropertyPath(propertyPath)\n            const relation = metadata.findRelationWithPropertyPath(propertyPath)\n\n            if (!embed && !column && !relation)\n                throw new EntityPropertyNotFoundError(propertyPath, metadata)\n\n            if (column) {\n                let direction =\n                    typeof order[key] === \"object\"\n                        ? (order[key] as any).direction\n                        : order[key]\n                direction =\n                    direction === \"DESC\" ||\n                    direction === \"desc\" ||\n                    direction === -1\n                        ? \"DESC\"\n                        : \"ASC\"\n                let nulls =\n                    typeof order[key] === \"object\"\n                        ? (order[key] as any).nulls\n                        : undefined\n                nulls =\n                    nulls?.toLowerCase() === \"first\"\n                        ? \"NULLS FIRST\"\n                        : nulls?.toLowerCase() === \"last\"\n                        ? \"NULLS LAST\"\n                        : undefined\n\n                const aliasPath = `${alias}.${propertyPath}`\n                // const selection = this.expressionMap.selects.find(\n                //     (s) => s.selection === aliasPath,\n                // )\n                // if (selection) {\n                //     // this is not building correctly now???\n                //     aliasPath = this.escape(\n                //         DriverUtils.buildAlias(\n                //             this.connection.driver,\n                //             undefined,\n                //             alias,\n                //             column.databaseName,\n                //         ),\n                //     )\n                //     // selection.aliasName = aliasPath\n                // } else {\n                //     if (column.isVirtualProperty && column.query) {\n                //         aliasPath = `(${column.query(alias)})`\n                //     }\n                // }\n\n                // console.log(\"add sort\", selection, aliasPath, direction, nulls)\n                this.addOrderBy(aliasPath, direction, nulls)\n                // this.orderBys.push({ alias: alias + \".\" + propertyPath, direction, nulls });\n            } else if (embed) {\n                this.buildOrder(\n                    order[key] as FindOptionsOrder<any>,\n                    metadata,\n                    alias,\n                    propertyPath,\n                )\n            } else if (relation) {\n                let joinAlias = alias + \"_\" + propertyPath.replace(\".\", \"_\")\n                joinAlias = DriverUtils.buildAlias(\n                    this.connection.driver,\n                    { joiner: \"__\" },\n                    alias,\n                    joinAlias,\n                )\n                // console.log(\"joinAlias\", joinAlias, joinAlias.length, this.connection.driver.maxAliasLength)\n                // todo: use expressionMap.joinAttributes, and create a new one using\n                //  const joinAttribute = new JoinAttribute(this.connection, this.expressionMap);\n\n                const existJoin = this.joins.find(\n                    (join) => join.alias === joinAlias,\n                )\n                if (!existJoin) {\n                    this.joins.push({\n                        type: \"left\",\n                        select: false,\n                        alias: joinAlias,\n                        parentAlias: alias,\n                        selection: undefined,\n                        relationMetadata: relation,\n                    })\n                }\n                this.buildOrder(\n                    order[key] as FindOptionsOrder<any>,\n                    relation.inverseEntityMetadata,\n                    joinAlias,\n                )\n            }\n        }\n    }\n\n    protected buildWhere(\n        where: FindOptionsWhere<any>[] | FindOptionsWhere<any>,\n        metadata: EntityMetadata,\n        alias: string,\n        embedPrefix?: string,\n    ) {\n        let condition: string = \"\"\n        // let parameterIndex = Object.keys(this.expressionMap.nativeParameters).length;\n        if (Array.isArray(where)) {\n            if (where.length) {\n                condition = where\n                    .map((whereItem) => {\n                        return this.buildWhere(\n                            whereItem,\n                            metadata,\n                            alias,\n                            embedPrefix,\n                        )\n                    })\n                    .filter((condition) => !!condition)\n                    .map((condition) => \"(\" + condition + \")\")\n                    .join(\" OR \")\n            }\n        } else {\n            const andConditions: string[] = []\n            for (const key in where) {\n                if (where[key] === undefined || where[key] === null) continue\n\n                const propertyPath = embedPrefix ? embedPrefix + \".\" + key : key\n                const column =\n                    metadata.findColumnWithPropertyPathStrict(propertyPath)\n                const embed =\n                    metadata.findEmbeddedWithPropertyPath(propertyPath)\n                const relation =\n                    metadata.findRelationWithPropertyPath(propertyPath)\n\n                if (!embed && !column && !relation)\n                    throw new EntityPropertyNotFoundError(\n                        propertyPath,\n                        metadata,\n                    )\n\n                if (column) {\n                    let aliasPath = `${alias}.${propertyPath}`\n                    if (column.isVirtualProperty && column.query) {\n                        aliasPath = `(${column.query(this.escape(alias))})`\n                    }\n                    // const parameterName = alias + \"_\" + propertyPath.split(\".\").join(\"_\") + \"_\" + parameterIndex;\n\n                    // todo: we need to handle other operators as well?\n                    let parameterValue = where[key]\n                    if (InstanceChecker.isEqualOperator(where[key])) {\n                        parameterValue = where[key].value\n                    }\n\n                    if (column.transformer) {\n                        if (parameterValue instanceof FindOperator) {\n                            parameterValue.transformValue(column.transformer)\n                        } else {\n                            parameterValue = ApplyValueTransformers.transformTo(\n                                column.transformer,\n                                parameterValue,\n                            )\n                        }\n                    }\n\n                    // MSSQL requires parameters to carry extra type information\n                    if (this.connection.driver.options.type === \"mssql\") {\n                        parameterValue = (\n                            this.connection.driver as SqlServerDriver\n                        ).parametrizeValues(column, parameterValue)\n                    }\n\n                    // if (parameterValue === null) {\n                    //     andConditions.push(`${aliasPath} IS NULL`);\n                    //\n                    // } else if (parameterValue instanceof FindOperator) {\n                    //     // let parameters: any[] = [];\n                    //     // if (parameterValue.useParameter) {\n                    //     //     const realParameterValues: any[] = parameterValue.multipleParameters ? parameterValue.value : [parameterValue.value];\n                    //     //     realParameterValues.forEach((realParameterValue, realParameterValueIndex) => {\n                    //     //\n                    //     //         // don't create parameters for number to prevent max number of variables issues as much as possible\n                    //     //         if (typeof realParameterValue === \"number\") {\n                    //     //             parameters.push(realParameterValue);\n                    //     //\n                    //     //         } else {\n                    //     //             this.expressionMap.nativeParameters[parameterName + realParameterValueIndex] = realParameterValue;\n                    //     //             parameterIndex++;\n                    //     //             parameters.push(this.connection.driver.createParameter(parameterName + realParameterValueIndex, parameterIndex - 1));\n                    //     //         }\n                    //     //     });\n                    //     // }\n                    //     andConditions.push(\n                    //         this.createWhereConditionExpression(this.getWherePredicateCondition(aliasPath, parameterValue))\n                    //         // parameterValue.toSql(this.connection, aliasPath, parameters));\n                    //     )\n                    //\n                    // } else {\n                    //     this.expressionMap.nativeParameters[parameterName] = parameterValue;\n                    //     parameterIndex++;\n                    //     const parameter = this.connection.driver.createParameter(parameterName, parameterIndex - 1);\n                    //     andConditions.push(`${aliasPath} = ${parameter}`);\n                    // }\n\n                    andConditions.push(\n                        this.createWhereConditionExpression(\n                            this.getWherePredicateCondition(\n                                aliasPath,\n                                parameterValue,\n                            ),\n                        ),\n                        // parameterValue.toSql(this.connection, aliasPath, parameters));\n                    )\n\n                    // this.conditions.push(`${alias}.${propertyPath} = :${paramName}`);\n                    // this.expressionMap.parameters[paramName] = where[key]; // todo: handle functions and other edge cases\n                } else if (embed) {\n                    const condition = this.buildWhere(\n                        where[key],\n                        metadata,\n                        alias,\n                        propertyPath,\n                    )\n                    if (condition) andConditions.push(condition)\n                } else if (relation) {\n                    // if all properties of where are undefined we don't need to join anything\n                    // this can happen when user defines map with conditional queries inside\n                    if (typeof where[key] === \"object\") {\n                        const allAllUndefined = Object.keys(where[key]).every(\n                            (k) => where[key][k] === undefined,\n                        )\n                        if (allAllUndefined) {\n                            continue\n                        }\n                    }\n\n                    if (InstanceChecker.isFindOperator(where[key])) {\n                        if (\n                            where[key].type === \"moreThan\" ||\n                            where[key].type === \"lessThan\" ||\n                            where[key].type === \"moreThanOrEqual\" ||\n                            where[key].type === \"lessThanOrEqual\"\n                        ) {\n                            let sqlOperator = \"\"\n                            if (where[key].type === \"moreThan\") {\n                                sqlOperator = \">\"\n                            } else if (where[key].type === \"lessThan\") {\n                                sqlOperator = \"<\"\n                            } else if (where[key].type === \"moreThanOrEqual\") {\n                                sqlOperator = \">=\"\n                            } else if (where[key].type === \"lessThanOrEqual\") {\n                                sqlOperator = \"<=\"\n                            }\n                            // basically relation count functionality\n                            const qb: QueryBuilder<any> = this.subQuery()\n                            if (relation.isManyToManyOwner) {\n                                qb.select(\"COUNT(*)\")\n                                    .from(\n                                        relation.joinTableName,\n                                        relation.joinTableName,\n                                    )\n                                    .where(\n                                        relation.joinColumns\n                                            .map((column) => {\n                                                return `${\n                                                    relation.joinTableName\n                                                }.${\n                                                    column.propertyName\n                                                } = ${alias}.${\n                                                    column.referencedColumn!\n                                                        .propertyName\n                                                }`\n                                            })\n                                            .join(\" AND \"),\n                                    )\n                            } else if (relation.isManyToManyNotOwner) {\n                                qb.select(\"COUNT(*)\")\n                                    .from(\n                                        relation.inverseRelation!.joinTableName,\n                                        relation.inverseRelation!.joinTableName,\n                                    )\n                                    .where(\n                                        relation\n                                            .inverseRelation!.inverseJoinColumns.map(\n                                                (column) => {\n                                                    return `${\n                                                        relation.inverseRelation!\n                                                            .joinTableName\n                                                    }.${\n                                                        column.propertyName\n                                                    } = ${alias}.${\n                                                        column.referencedColumn!\n                                                            .propertyName\n                                                    }`\n                                                },\n                                            )\n                                            .join(\" AND \"),\n                                    )\n                            } else if (relation.isOneToMany) {\n                                qb.select(\"COUNT(*)\")\n                                    .from(\n                                        relation.inverseEntityMetadata.target,\n                                        relation.inverseEntityMetadata\n                                            .tableName,\n                                    )\n                                    .where(\n                                        relation\n                                            .inverseRelation!.joinColumns.map(\n                                                (column) => {\n                                                    return `${\n                                                        relation\n                                                            .inverseEntityMetadata\n                                                            .tableName\n                                                    }.${\n                                                        column.propertyName\n                                                    } = ${alias}.${\n                                                        column.referencedColumn!\n                                                            .propertyName\n                                                    }`\n                                                },\n                                            )\n                                            .join(\" AND \"),\n                                    )\n                            } else {\n                                throw new Error(\n                                    `This relation isn't supported by given find operator`,\n                                )\n                            }\n                            // this\n                            //     .addSelect(qb.getSql(), relation.propertyAliasName + \"_cnt\")\n                            //     .andWhere(this.escape(relation.propertyAliasName + \"_cnt\") + \" \" + sqlOperator + \" \" + parseInt(where[key].value));\n                            this.andWhere(\n                                qb.getSql() +\n                                    \" \" +\n                                    sqlOperator +\n                                    \" \" +\n                                    parseInt(where[key].value),\n                            )\n                        } else {\n                            if (\n                                relation.isManyToOne ||\n                                (relation.isOneToOne &&\n                                    relation.isOneToOneOwner)\n                            ) {\n                                const aliasPath = `${alias}.${propertyPath}`\n\n                                andConditions.push(\n                                    this.createWhereConditionExpression(\n                                        this.getWherePredicateCondition(\n                                            aliasPath,\n                                            where[key],\n                                        ),\n                                    ),\n                                )\n                            } else {\n                                throw new Error(\n                                    `This relation isn't supported by given find operator`,\n                                )\n                            }\n                        }\n                    } else {\n                        // const joinAlias = alias + \"_\" + relation.propertyName;\n                        let joinAlias =\n                            alias +\n                            \"_\" +\n                            relation.propertyPath.replace(\".\", \"_\")\n                        joinAlias = DriverUtils.buildAlias(\n                            this.connection.driver,\n                            { joiner: \"__\" },\n                            alias,\n                            joinAlias,\n                        )\n\n                        const existJoin = this.joins.find(\n                            (join) => join.alias === joinAlias,\n                        )\n                        if (!existJoin) {\n                            this.joins.push({\n                                type: \"left\",\n                                select: false,\n                                selection: undefined,\n                                alias: joinAlias,\n                                parentAlias: alias,\n                                relationMetadata: relation,\n                            })\n                        }\n\n                        const condition = this.buildWhere(\n                            where[key],\n                            relation.inverseEntityMetadata,\n                            joinAlias,\n                        )\n                        if (condition) {\n                            andConditions.push(condition)\n                            // parameterIndex = Object.keys(this.expressionMap.nativeParameters).length;\n                        }\n                    }\n                }\n            }\n            condition = andConditions.length\n                ? \"(\" + andConditions.join(\") AND (\") + \")\"\n                : andConditions.join(\" AND \")\n        }\n        return condition.length ? \"(\" + condition + \")\" : condition\n    }\n}\n"], "sourceRoot": ".."}