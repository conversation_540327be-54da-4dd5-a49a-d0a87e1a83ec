{"version": 3, "sources": ["../../src/schema-builder/table/TableIndex.ts"], "names": [], "mappings": ";;;AAGA;;GAEG;AACH,MAAa,UAAU;IA4DnB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,OAA0B;QA/D7B,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;QAWjD;;WAEG;QACH,gBAAW,GAAa,EAAE,CAAA;QAkDtB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;QACxB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;QACtC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAA;QAClC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAA;QACpC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,OAAO,CAAC,YAAY,CAAA;QAC1C,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,CAAA;QACtC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,OAAO,CAAC,cAAc,CAAA;QAC9C,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;QAC5B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAA;IACnD,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK;QACD,OAAO,IAAI,UAAU,CAAoB;YACrC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;YAClC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;SACpB,CAAC,CAAA;IACN,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,aAA4B;QACtC,OAAO,IAAI,UAAU,CAAoB;YACrC,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,WAAW,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,CAClC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,CAClC;YACD,QAAQ,EAAE,aAAa,CAAC,QAAQ;YAChC,SAAS,EAAE,aAAa,CAAC,SAAS;YAClC,YAAY,EAAE,aAAa,CAAC,YAAY;YACxC,UAAU,EAAE,aAAa,CAAC,UAAU;YACpC,cAAc,EAAE,aAAa,CAAC,cAAc;YAC5C,MAAM,EAAE,aAAa,CAAC,MAAM;YAC5B,KAAK,EAAE,aAAa,CAAC,KAAK;SAC7B,CAAC,CAAA;IACN,CAAC;CACJ;AAvHD,gCAuHC", "file": "TableIndex.js", "sourcesContent": ["import { IndexMetadata } from \"../../metadata/IndexMetadata\"\nimport { TableIndexOptions } from \"../options/TableIndexOptions\"\n\n/**\n * Database's table index stored in this class.\n */\nexport class TableIndex {\n    readonly \"@instanceof\" = Symbol.for(\"TableIndex\")\n\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Index name.\n     */\n    name?: string\n\n    /**\n     * Columns included in this index.\n     */\n    columnNames: string[] = []\n\n    /**\n     * Indicates if this index is unique.\n     */\n    isUnique: boolean\n\n    /**\n     * The SPATIAL modifier indexes the entire column and does not allow indexed columns to contain NULL values.\n     * Works only in MySQL.\n     */\n    isSpatial: boolean\n\n    /**\n     * Create the index using the CONCURRENTLY modifier\n     * Works only in postgres.\n     */\n    isConcurrent: boolean\n\n    /**\n     * The FULLTEXT modifier indexes the entire column and does not allow prefixing.\n     * Works only in MySQL.\n     */\n    isFulltext: boolean\n\n    /**\n     * NULL_FILTERED indexes are particularly useful for indexing sparse columns, where most rows contain a NULL value.\n     * In these cases, the NULL_FILTERED index can be considerably smaller and more efficient to maintain than\n     * a normal index that includes NULL values.\n     *\n     * Works only in Spanner.\n     */\n    isNullFiltered: boolean\n\n    /**\n     * Fulltext parser.\n     * Works only in MySQL.\n     */\n    parser?: string\n\n    /**\n     * Index filter condition.\n     */\n    where: string\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(options: TableIndexOptions) {\n        this.name = options.name\n        this.columnNames = options.columnNames\n        this.isUnique = !!options.isUnique\n        this.isSpatial = !!options.isSpatial\n        this.isConcurrent = !!options.isConcurrent\n        this.isFulltext = !!options.isFulltext\n        this.isNullFiltered = !!options.isNullFiltered\n        this.parser = options.parser\n        this.where = options.where ? options.where : \"\"\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates a new copy of this index with exactly same properties.\n     */\n    clone(): TableIndex {\n        return new TableIndex(<TableIndexOptions>{\n            name: this.name,\n            columnNames: [...this.columnNames],\n            isUnique: this.isUnique,\n            isSpatial: this.isSpatial,\n            isConcurrent: this.isConcurrent,\n            isFulltext: this.isFulltext,\n            isNullFiltered: this.isNullFiltered,\n            parser: this.parser,\n            where: this.where,\n        })\n    }\n\n    // -------------------------------------------------------------------------\n    // Static Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates index from the index metadata object.\n     */\n    static create(indexMetadata: IndexMetadata): TableIndex {\n        return new TableIndex(<TableIndexOptions>{\n            name: indexMetadata.name,\n            columnNames: indexMetadata.columns.map(\n                (column) => column.databaseName,\n            ),\n            isUnique: indexMetadata.isUnique,\n            isSpatial: indexMetadata.isSpatial,\n            isConcurrent: indexMetadata.isConcurrent,\n            isFulltext: indexMetadata.isFulltext,\n            isNullFiltered: indexMetadata.isNullFiltered,\n            parser: indexMetadata.parser,\n            where: indexMetadata.where,\n        })\n    }\n}\n"], "sourceRoot": "../.."}