{"name": "<PERSON><PERSON><PERSON>_back", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "start": "node dist/server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"dotenv": "^16.5.0", "express": "^5.1.0", "mysql2": "^3.14.1", "reflect-metadata": "^0.2.2", "typeorm": "^0.3.24"}, "devDependencies": {"@types/express": "^5.0.2", "@types/node": "^22.15.29", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}