{"version": 3, "sources": ["../../src/schema-builder/options/TableExclusionOptions.ts"], "names": [], "mappings": "", "file": "TableExclusionOptions.js", "sourcesContent": ["/**\n * Database's table exclusion constraint options.\n */\nexport interface TableExclusionOptions {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Constraint name.\n     */\n    name?: string\n\n    /**\n     * Exclusion expression.\n     */\n    expression?: string\n}\n"], "sourceRoot": "../.."}