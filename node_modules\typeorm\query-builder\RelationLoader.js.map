{"version": 3, "sources": ["../../src/query-builder/RelationLoader.ts"], "names": [], "mappings": ";;;AAIA,uEAAmE;AAGnE;;;GAGG;AACH,MAAa,cAAc;IACvB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAoB,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IAAG,CAAC;IAE9C,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,IAAI,CACA,QAA0B,EAC1B,gBAAiD,EACjD,WAAyB,EACzB,YAAsC;QAEtC,iDAAiD;QACjD,IAAI,WAAW,IAAI,WAAW,CAAC,UAAU;YAAE,WAAW,GAAG,SAAS,CAAA,CAAC,gCAAgC;QACnG,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC,4BAA4B,CACpC,QAAQ,EACR,gBAAgB,EAChB,WAAW,EACX,YAAY,CACf,CAAA;QACL,CAAC;aAAM,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;YAC7D,OAAO,IAAI,CAAC,+BAA+B,CACvC,QAAQ,EACR,gBAAgB,EAChB,WAAW,EACX,YAAY,CACf,CAAA;QACL,CAAC;aAAM,IAAI,QAAQ,CAAC,iBAAiB,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC,mBAAmB,CAC3B,QAAQ,EACR,gBAAgB,EAChB,WAAW,EACX,YAAY,CACf,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,yBAAyB;YACzB,OAAO,IAAI,CAAC,sBAAsB,CAC9B,QAAQ,EACR,gBAAgB,EAChB,WAAW,EACX,YAAY,CACf,CAAA;QACL,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,4BAA4B,CACxB,QAA0B,EAC1B,gBAAiD,EACjD,WAAyB,EACzB,YAAsC;QAEtC,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC;YAC5C,CAAC,CAAC,gBAAgB;YAClB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAA;QAExB,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAA;QAClD,MAAM,EAAE,GAAG,YAAY;YACnB,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,IAAI,CAAC,UAAU;iBACV,kBAAkB,CAAC,WAAW,CAAC;iBAC/B,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,WAAW;iBACzC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAA;QAErD,MAAM,SAAS,GAAG,EAAE,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI,CAAA;QAClD,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAA;QACtD,MAAM,WAAW,GAAG,QAAQ,CAAC,QAAQ;YACjC,CAAC,CAAC,QAAQ,CAAC,WAAW;YACtB,CAAC,CAAC,QAAQ,CAAC,eAAgB,CAAC,WAAW,CAAA;QAC3C,MAAM,UAAU,GAAG,WAAW;aACzB,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;YAChB,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,IAAI,IAClC,UAAU,CAAC,YACf,MAAM,SAAS,IAAI,UAAU,CAAC,gBAAiB,CAAC,YAAY,EAAE,CAAA;QAClE,CAAC,CAAC;aACD,IAAI,CAAC,OAAO,CAAC,CAAA;QAElB,EAAE,CAAC,SAAS,CACR,QAAQ,CAAC,cAAc,CAAC,MAAkB,EAC1C,aAAa,EACb,UAAU,CACb,CAAA;QAED,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,EAAE,CAAC,KAAK,CACJ,GAAG,aAAa,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,YACvC,aAAa,GAAG,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,YACrC,GAAG,CACN,CAAA;YACD,EAAE,CAAC,YAAY,CACX,aAAa,GAAG,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,EAC7C,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CACpB,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAC1C,CACJ,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,SAAS,GAAG,QAAQ;iBACrB,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;gBACzB,OAAO,OAAO;qBACT,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;oBACzB,MAAM,SAAS,GACX,aAAa;wBACb,UAAU;wBACV,WAAW;wBACX,GAAG;wBACH,WAAW,CAAA;oBACf,EAAE,CAAC,YAAY,CACX,SAAS,EACT,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CACtC,CAAA;oBACD,OAAO,CACH,aAAa;wBACb,GAAG;wBACH,MAAM,CAAC,YAAY;wBACnB,MAAM;wBACN,SAAS,CACZ,CAAA;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,OAAO,CAAC,CAAA;YACtB,CAAC,CAAC;iBACD,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG,CAAC;iBACzC,IAAI,CAAC,MAAM,CAAC,CAAA;YACjB,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;QACvB,CAAC;QAED,mCAAgB,CAAC,kBAAkB,CAC/B,EAAE,EACF,EAAE,CAAC,KAAK,EACR,EAAE,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CACvC,CAAA;QAED,OAAO,EAAE,CAAC,OAAO,EAAE,CAAA;QACnB,2CAA2C;IAC/C,CAAC;IAED;;;;;;OAMG;IACH,+BAA+B,CAC3B,QAA0B,EAC1B,gBAAiD,EACjD,WAAyB,EACzB,YAAsC;QAEtC,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC;YAC5C,CAAC,CAAC,gBAAgB;YAClB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAA;QACxB,MAAM,OAAO,GAAG,QAAQ,CAAC,eAAgB,CAAC,WAAW,CAAA;QACrD,MAAM,EAAE,GAAG,YAAY;YACnB,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,IAAI,CAAC,UAAU;iBACV,kBAAkB,CAAC,WAAW,CAAC;iBAC/B,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC;iBAC7B,IAAI,CACD,QAAQ,CAAC,eAAgB,CAAC,cAAc,CAAC,MAAM,EAC/C,QAAQ,CAAC,YAAY,CACxB,CAAA;QAEX,MAAM,SAAS,GAAG,EAAE,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI,CAAA;QAElD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,EAAE,CAAC,KAAK,CACJ,GAAG,SAAS,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,YACnC,SAAS,GAAG,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,YACjC,GAAG,CACN,CAAA;YACD,EAAE,CAAC,YAAY,CACX,SAAS,GAAG,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,EACzC,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CACpB,OAAO,CAAC,CAAC,CAAC,CAAC,gBAAiB,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAC5D,CACJ,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,SAAS,GAAG,QAAQ;iBACrB,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;gBACzB,OAAO,OAAO;qBACT,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;oBACzB,MAAM,SAAS,GACX,SAAS;wBACT,UAAU;wBACV,WAAW;wBACX,GAAG;wBACH,WAAW,CAAA;oBACf,EAAE,CAAC,YAAY,CACX,SAAS,EACT,MAAM,CAAC,gBAAiB,CAAC,cAAc,CACnC,MAAM,EACN,IAAI,CACP,CACJ,CAAA;oBACD,OAAO,CACH,SAAS;wBACT,GAAG;wBACH,MAAM,CAAC,YAAY;wBACnB,MAAM;wBACN,SAAS,CACZ,CAAA;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,OAAO,CAAC,CAAA;YACtB,CAAC,CAAC;iBACD,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG,CAAC;iBACzC,IAAI,CAAC,MAAM,CAAC,CAAA;YACjB,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;QACvB,CAAC;QAED,mCAAgB,CAAC,kBAAkB,CAC/B,EAAE,EACF,EAAE,CAAC,KAAK,EACR,EAAE,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CACvC,CAAA;QAED,OAAO,EAAE,CAAC,OAAO,EAAE,CAAA;QACnB,iFAAiF;IACrF,CAAC;IAED;;;;;;;;OAQG;IACH,mBAAmB,CACf,QAA0B,EAC1B,gBAAiD,EACjD,WAAyB,EACzB,YAAsC;QAEtC,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC;YAC5C,CAAC,CAAC,gBAAgB;YAClB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAA;QACxB,MAAM,UAAU,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CAC1C,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE;YACvB,UAAU,CAAC,UAAU,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAC1D,UAAU,CAAC,gBAAiB,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAC5D,CAAA;YACD,OAAO,UAAU,CAAA;QACrB,CAAC,EACD,EAAmB,CACtB,CAAA;QAED,MAAM,EAAE,GAAG,YAAY;YACnB,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,IAAI,CAAC,UAAU;iBACV,kBAAkB,CAAC,WAAW,CAAC;iBAC/B,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC;iBAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAA;QAErD,MAAM,SAAS,GAAG,EAAE,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI,CAAA;QAClD,MAAM,SAAS,GAAG,QAAQ,CAAC,sBAAuB,CAAC,SAAS,CAAA;QAC5D,MAAM,oBAAoB,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;YACjE,OAAO,GAAG,SAAS,IAAI,UAAU,CAAC,YAAY,YAAY,UAAU,CAAC,YAAY,GAAG,CAAA;QACxF,CAAC,CAAC,CAAA;QACF,MAAM,2BAA2B,GAAG,QAAQ,CAAC,kBAAkB,CAAC,GAAG,CAC/D,CAAC,iBAAiB,EAAE,EAAE;YAClB,OAAO,GAAG,SAAS,IACf,iBAAiB,CAAC,YACtB,IAAI,SAAS,IACT,iBAAiB,CAAC,gBAAiB,CAAC,YACxC,EAAE,CAAA;QACN,CAAC,CACJ,CAAA;QAED,EAAE,CAAC,SAAS,CACR,SAAS,EACT,SAAS,EACT,CAAC,GAAG,oBAAoB,EAAE,GAAG,2BAA2B,CAAC,CAAC,IAAI,CAC1D,OAAO,CACV,CACJ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAE3B,mCAAgB,CAAC,kBAAkB,CAC/B,EAAE,EACF,EAAE,CAAC,KAAK,EACR,EAAE,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CACvC,CAAA;QAED,OAAO,EAAE,CAAC,OAAO,EAAE,CAAA;IACvB,CAAC;IAED;;;;;;;;OAQG;IACH,sBAAsB,CAClB,QAA0B,EAC1B,gBAAiD,EACjD,WAAyB,EACzB,YAAsC;QAEtC,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC;YAC5C,CAAC,CAAC,gBAAgB;YAClB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAA;QAExB,MAAM,EAAE,GAAG,YAAY;YACnB,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,IAAI,CAAC,UAAU;iBACV,kBAAkB,CAAC,WAAW,CAAC;iBAC/B,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC;iBAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAA;QAErD,MAAM,SAAS,GAAG,EAAE,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI,CAAA;QAClD,MAAM,SAAS,GAAG,QAAQ,CAAC,sBAAuB,CAAC,SAAS,CAAA;QAC5D,MAAM,oBAAoB,GAAG,QAAQ,CAAC,eAAgB,CAAC,WAAW,CAAC,GAAG,CAClE,CAAC,UAAU,EAAE,EAAE;YACX,OAAO,GAAG,SAAS,IACf,UAAU,CAAC,YACf,MAAM,SAAS,IAAI,UAAU,CAAC,gBAAiB,CAAC,YAAY,EAAE,CAAA;QAClE,CAAC,CACJ,CAAA;QACD,MAAM,2BAA2B,GAC7B,QAAQ,CAAC,eAAgB,CAAC,kBAAkB,CAAC,GAAG,CAC5C,CAAC,iBAAiB,EAAE,EAAE;YAClB,OAAO,GAAG,SAAS,IAAI,iBAAiB,CAAC,YAAY,YAAY,iBAAiB,CAAC,YAAY,GAAG,CAAA;QACtG,CAAC,CACJ,CAAA;QACL,MAAM,UAAU,GAAG,QAAQ,CAAC,eAAgB,CAAC,kBAAkB,CAAC,MAAM,CAClE,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE;YACvB,UAAU,CAAC,UAAU,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAC1D,UAAU,CAAC,gBAAiB,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAC5D,CAAA;YACD,OAAO,UAAU,CAAA;QACrB,CAAC,EACD,EAAmB,CACtB,CAAA;QAED,EAAE,CAAC,SAAS,CACR,SAAS,EACT,SAAS,EACT,CAAC,GAAG,oBAAoB,EAAE,GAAG,2BAA2B,CAAC,CAAC,IAAI,CAC1D,OAAO,CACV,CACJ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAE3B,mCAAgB,CAAC,kBAAkB,CAC/B,EAAE,EACF,EAAE,CAAC,KAAK,EACR,EAAE,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CACvC,CAAA;QAED,OAAO,EAAE,CAAC,OAAO,EAAE,CAAA;IACvB,CAAC;IAED;;;OAGG;IACH,cAAc,CACV,QAA0B,EAC1B,MAAqB,EACrB,WAAyB;QAEzB,MAAM,cAAc,GAAG,IAAI,CAAA;QAC3B,MAAM,SAAS,GAAG,IAAI,GAAG,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAA,CAAC,4DAA4D;QAClH,MAAM,YAAY,GAAG,YAAY,GAAG,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAA,CAAC,gEAAgE;QACjI,MAAM,YAAY,GAAG,QAAQ,GAAG,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAA,CAAC,kGAAkG;QAE/J,MAAM,OAAO,GAAG,CAAC,MAAqB,EAAE,KAAU,EAAE,EAAE;YAClD,MAAM,CAAC,SAAS,CAAC,GAAG,KAAK,CAAA;YACzB,MAAM,CAAC,YAAY,CAAC,GAAG,IAAI,CAAA;YAC3B,OAAO,MAAM,CAAC,YAAY,CAAC,CAAA;YAC3B,OAAO,KAAK,CAAA;QAChB,CAAC,CAAA;QACD,MAAM,UAAU,GAAG,CAAC,MAAqB,EAAE,KAAmB,EAAE,EAAE;YAC9D,OAAO,MAAM,CAAC,YAAY,CAAC,CAAA;YAC3B,OAAO,MAAM,CAAC,SAAS,CAAC,CAAA;YACxB,MAAM,CAAC,YAAY,CAAC,GAAG,KAAK,CAAA;YAC5B,KAAK,CAAC,IAAI;YACN,6CAA6C;YAC7C,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,YAAY,CAAC,KAAK,KAAK;gBAC1B,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC;gBACzB,CAAC,CAAC,MAAM,CACnB,CAAA;YACD,OAAO,KAAK,CAAA;QAChB,CAAC,CAAA;QAED,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,QAAQ,CAAC,YAAY,EAAE;YACjD,GAAG,EAAE;gBACD,IACI,IAAI,CAAC,YAAY,CAAC,KAAK,IAAI;oBAC3B,IAAI,CAAC,SAAS,CAAC,KAAK,SAAS;oBAE7B,2DAA2D;oBAC3D,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAA;gBAE3C,IAAI,IAAI,CAAC,YAAY,CAAC;oBAClB,2EAA2E;oBAC3E,OAAO,IAAI,CAAC,YAAY,CAAC,CAAA;gBAE7B,0FAA0F;gBAC1F,MAAM,MAAM,GAAG,cAAc;qBACxB,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,WAAW,CAAC;qBACjC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CACb,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,WAAW;oBACvC,CAAC,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC;wBACjB,CAAC,CAAC,IAAI;wBACN,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,MAAM,CACf,CAAA;gBACL,OAAO,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;YACnC,CAAC;YACD,GAAG,EAAE,UAAU,KAAyB;gBACpC,IAAI,KAAK,YAAY,OAAO,EAAE,CAAC;oBAC3B,4EAA4E;oBAC5E,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;gBAC3B,CAAC;qBAAM,CAAC;oBACJ,gEAAgE;oBAChE,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;gBACxB,CAAC;YACL,CAAC;YACD,YAAY,EAAE,IAAI;YAClB,UAAU,EAAE,KAAK;SACpB,CAAC,CAAA;IACN,CAAC;CACJ;AA1bD,wCA0bC", "file": "RelationLoader.js", "sourcesContent": ["import { DataSource } from \"../data-source/DataSource\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { RelationMetadata } from \"../metadata/RelationMetadata\"\nimport { FindOptionsUtils } from \"../find-options/FindOptionsUtils\"\nimport { SelectQueryBuilder } from \"./SelectQueryBuilder\"\n\n/**\n * Wraps entities and creates getters/setters for their relations\n * to be able to lazily load relations when accessing these relations.\n */\nexport class RelationLoader {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(private connection: DataSource) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Loads relation data for the given entity and its relation.\n     */\n    load(\n        relation: RelationMetadata,\n        entityOrEntities: ObjectLiteral | ObjectLiteral[],\n        queryRunner?: QueryRunner,\n        queryBuilder?: SelectQueryBuilder<any>,\n    ): Promise<any[]> {\n        // todo: check all places where it uses non array\n        if (queryRunner && queryRunner.isReleased) queryRunner = undefined // get new one if already closed\n        if (relation.isManyToOne || relation.isOneToOneOwner) {\n            return this.loadManyToOneOrOneToOneOwner(\n                relation,\n                entityOrEntities,\n                queryRunner,\n                queryBuilder,\n            )\n        } else if (relation.isOneToMany || relation.isOneToOneNotOwner) {\n            return this.loadOneToManyOrOneToOneNotOwner(\n                relation,\n                entityOrEntities,\n                queryRunner,\n                queryBuilder,\n            )\n        } else if (relation.isManyToManyOwner) {\n            return this.loadManyToManyOwner(\n                relation,\n                entityOrEntities,\n                queryRunner,\n                queryBuilder,\n            )\n        } else {\n            // many-to-many non owner\n            return this.loadManyToManyNotOwner(\n                relation,\n                entityOrEntities,\n                queryRunner,\n                queryBuilder,\n            )\n        }\n    }\n\n    /**\n     * Loads data for many-to-one and one-to-one owner relations.\n     *\n     * (ow) post.category<=>category.post\n     * loaded: category from post\n     * example: SELECT category.id AS category_id, category.name AS category_name FROM category category\n     *              INNER JOIN post Post ON Post.category=category.id WHERE Post.id=1\n     */\n    loadManyToOneOrOneToOneOwner(\n        relation: RelationMetadata,\n        entityOrEntities: ObjectLiteral | ObjectLiteral[],\n        queryRunner?: QueryRunner,\n        queryBuilder?: SelectQueryBuilder<any>,\n    ): Promise<any> {\n        const entities = Array.isArray(entityOrEntities)\n            ? entityOrEntities\n            : [entityOrEntities]\n\n        const joinAliasName = relation.entityMetadata.name\n        const qb = queryBuilder\n            ? queryBuilder\n            : this.connection\n                  .createQueryBuilder(queryRunner)\n                  .select(relation.propertyName) // category\n                  .from(relation.type, relation.propertyName)\n\n        const mainAlias = qb.expressionMap.mainAlias!.name\n        const columns = relation.entityMetadata.primaryColumns\n        const joinColumns = relation.isOwning\n            ? relation.joinColumns\n            : relation.inverseRelation!.joinColumns\n        const conditions = joinColumns\n            .map((joinColumn) => {\n                return `${relation.entityMetadata.name}.${\n                    joinColumn.propertyName\n                } = ${mainAlias}.${joinColumn.referencedColumn!.propertyName}`\n            })\n            .join(\" AND \")\n\n        qb.innerJoin(\n            relation.entityMetadata.target as Function,\n            joinAliasName,\n            conditions,\n        )\n\n        if (columns.length === 1) {\n            qb.where(\n                `${joinAliasName}.${columns[0].propertyPath} IN (:...${\n                    joinAliasName + \"_\" + columns[0].propertyName\n                })`,\n            )\n            qb.setParameter(\n                joinAliasName + \"_\" + columns[0].propertyName,\n                entities.map((entity) =>\n                    columns[0].getEntityValue(entity, true),\n                ),\n            )\n        } else {\n            const condition = entities\n                .map((entity, entityIndex) => {\n                    return columns\n                        .map((column, columnIndex) => {\n                            const paramName =\n                                joinAliasName +\n                                \"_entity_\" +\n                                entityIndex +\n                                \"_\" +\n                                columnIndex\n                            qb.setParameter(\n                                paramName,\n                                column.getEntityValue(entity, true),\n                            )\n                            return (\n                                joinAliasName +\n                                \".\" +\n                                column.propertyPath +\n                                \" = :\" +\n                                paramName\n                            )\n                        })\n                        .join(\" AND \")\n                })\n                .map((condition) => \"(\" + condition + \")\")\n                .join(\" OR \")\n            qb.where(condition)\n        }\n\n        FindOptionsUtils.joinEagerRelations(\n            qb,\n            qb.alias,\n            qb.expressionMap.mainAlias!.metadata,\n        )\n\n        return qb.getMany()\n        // return qb.getOne(); todo: fix all usages\n    }\n\n    /**\n     * Loads data for one-to-many and one-to-one not owner relations.\n     *\n     * SELECT post\n     * FROM post post\n     * WHERE post.[joinColumn.name] = entity[joinColumn.referencedColumn]\n     */\n    loadOneToManyOrOneToOneNotOwner(\n        relation: RelationMetadata,\n        entityOrEntities: ObjectLiteral | ObjectLiteral[],\n        queryRunner?: QueryRunner,\n        queryBuilder?: SelectQueryBuilder<any>,\n    ): Promise<any> {\n        const entities = Array.isArray(entityOrEntities)\n            ? entityOrEntities\n            : [entityOrEntities]\n        const columns = relation.inverseRelation!.joinColumns\n        const qb = queryBuilder\n            ? queryBuilder\n            : this.connection\n                  .createQueryBuilder(queryRunner)\n                  .select(relation.propertyName)\n                  .from(\n                      relation.inverseRelation!.entityMetadata.target,\n                      relation.propertyName,\n                  )\n\n        const aliasName = qb.expressionMap.mainAlias!.name\n\n        if (columns.length === 1) {\n            qb.where(\n                `${aliasName}.${columns[0].propertyPath} IN (:...${\n                    aliasName + \"_\" + columns[0].propertyName\n                })`,\n            )\n            qb.setParameter(\n                aliasName + \"_\" + columns[0].propertyName,\n                entities.map((entity) =>\n                    columns[0].referencedColumn!.getEntityValue(entity, true),\n                ),\n            )\n        } else {\n            const condition = entities\n                .map((entity, entityIndex) => {\n                    return columns\n                        .map((column, columnIndex) => {\n                            const paramName =\n                                aliasName +\n                                \"_entity_\" +\n                                entityIndex +\n                                \"_\" +\n                                columnIndex\n                            qb.setParameter(\n                                paramName,\n                                column.referencedColumn!.getEntityValue(\n                                    entity,\n                                    true,\n                                ),\n                            )\n                            return (\n                                aliasName +\n                                \".\" +\n                                column.propertyPath +\n                                \" = :\" +\n                                paramName\n                            )\n                        })\n                        .join(\" AND \")\n                })\n                .map((condition) => \"(\" + condition + \")\")\n                .join(\" OR \")\n            qb.where(condition)\n        }\n\n        FindOptionsUtils.joinEagerRelations(\n            qb,\n            qb.alias,\n            qb.expressionMap.mainAlias!.metadata,\n        )\n\n        return qb.getMany()\n        // return relation.isOneToMany ? qb.getMany() : qb.getOne(); todo: fix all usages\n    }\n\n    /**\n     * Loads data for many-to-many owner relations.\n     *\n     * SELECT category\n     * FROM category category\n     * INNER JOIN post_categories post_categories\n     * ON post_categories.postId = :postId\n     * AND post_categories.categoryId = category.id\n     */\n    loadManyToManyOwner(\n        relation: RelationMetadata,\n        entityOrEntities: ObjectLiteral | ObjectLiteral[],\n        queryRunner?: QueryRunner,\n        queryBuilder?: SelectQueryBuilder<any>,\n    ): Promise<any> {\n        const entities = Array.isArray(entityOrEntities)\n            ? entityOrEntities\n            : [entityOrEntities]\n        const parameters = relation.joinColumns.reduce(\n            (parameters, joinColumn) => {\n                parameters[joinColumn.propertyName] = entities.map((entity) =>\n                    joinColumn.referencedColumn!.getEntityValue(entity, true),\n                )\n                return parameters\n            },\n            {} as ObjectLiteral,\n        )\n\n        const qb = queryBuilder\n            ? queryBuilder\n            : this.connection\n                  .createQueryBuilder(queryRunner)\n                  .select(relation.propertyName)\n                  .from(relation.type, relation.propertyName)\n\n        const mainAlias = qb.expressionMap.mainAlias!.name\n        const joinAlias = relation.junctionEntityMetadata!.tableName\n        const joinColumnConditions = relation.joinColumns.map((joinColumn) => {\n            return `${joinAlias}.${joinColumn.propertyName} IN (:...${joinColumn.propertyName})`\n        })\n        const inverseJoinColumnConditions = relation.inverseJoinColumns.map(\n            (inverseJoinColumn) => {\n                return `${joinAlias}.${\n                    inverseJoinColumn.propertyName\n                }=${mainAlias}.${\n                    inverseJoinColumn.referencedColumn!.propertyName\n                }`\n            },\n        )\n\n        qb.innerJoin(\n            joinAlias,\n            joinAlias,\n            [...joinColumnConditions, ...inverseJoinColumnConditions].join(\n                \" AND \",\n            ),\n        ).setParameters(parameters)\n\n        FindOptionsUtils.joinEagerRelations(\n            qb,\n            qb.alias,\n            qb.expressionMap.mainAlias!.metadata,\n        )\n\n        return qb.getMany()\n    }\n\n    /**\n     * Loads data for many-to-many not owner relations.\n     *\n     * SELECT post\n     * FROM post post\n     * INNER JOIN post_categories post_categories\n     * ON post_categories.postId = post.id\n     * AND post_categories.categoryId = post_categories.categoryId\n     */\n    loadManyToManyNotOwner(\n        relation: RelationMetadata,\n        entityOrEntities: ObjectLiteral | ObjectLiteral[],\n        queryRunner?: QueryRunner,\n        queryBuilder?: SelectQueryBuilder<any>,\n    ): Promise<any> {\n        const entities = Array.isArray(entityOrEntities)\n            ? entityOrEntities\n            : [entityOrEntities]\n\n        const qb = queryBuilder\n            ? queryBuilder\n            : this.connection\n                  .createQueryBuilder(queryRunner)\n                  .select(relation.propertyName)\n                  .from(relation.type, relation.propertyName)\n\n        const mainAlias = qb.expressionMap.mainAlias!.name\n        const joinAlias = relation.junctionEntityMetadata!.tableName\n        const joinColumnConditions = relation.inverseRelation!.joinColumns.map(\n            (joinColumn) => {\n                return `${joinAlias}.${\n                    joinColumn.propertyName\n                } = ${mainAlias}.${joinColumn.referencedColumn!.propertyName}`\n            },\n        )\n        const inverseJoinColumnConditions =\n            relation.inverseRelation!.inverseJoinColumns.map(\n                (inverseJoinColumn) => {\n                    return `${joinAlias}.${inverseJoinColumn.propertyName} IN (:...${inverseJoinColumn.propertyName})`\n                },\n            )\n        const parameters = relation.inverseRelation!.inverseJoinColumns.reduce(\n            (parameters, joinColumn) => {\n                parameters[joinColumn.propertyName] = entities.map((entity) =>\n                    joinColumn.referencedColumn!.getEntityValue(entity, true),\n                )\n                return parameters\n            },\n            {} as ObjectLiteral,\n        )\n\n        qb.innerJoin(\n            joinAlias,\n            joinAlias,\n            [...joinColumnConditions, ...inverseJoinColumnConditions].join(\n                \" AND \",\n            ),\n        ).setParameters(parameters)\n\n        FindOptionsUtils.joinEagerRelations(\n            qb,\n            qb.alias,\n            qb.expressionMap.mainAlias!.metadata,\n        )\n\n        return qb.getMany()\n    }\n\n    /**\n     * Wraps given entity and creates getters/setters for its given relation\n     * to be able to lazily load data when accessing this relation.\n     */\n    enableLazyLoad(\n        relation: RelationMetadata,\n        entity: ObjectLiteral,\n        queryRunner?: QueryRunner,\n    ) {\n        const relationLoader = this\n        const dataIndex = \"__\" + relation.propertyName + \"__\" // in what property of the entity loaded data will be stored\n        const promiseIndex = \"__promise_\" + relation.propertyName + \"__\" // in what property of the entity loading promise will be stored\n        const resolveIndex = \"__has_\" + relation.propertyName + \"__\" // indicates if relation data already was loaded or not, we need this flag if loaded data is empty\n\n        const setData = (entity: ObjectLiteral, value: any) => {\n            entity[dataIndex] = value\n            entity[resolveIndex] = true\n            delete entity[promiseIndex]\n            return value\n        }\n        const setPromise = (entity: ObjectLiteral, value: Promise<any>) => {\n            delete entity[resolveIndex]\n            delete entity[dataIndex]\n            entity[promiseIndex] = value\n            value.then(\n                // ensure different value is not assigned yet\n                (result) =>\n                    entity[promiseIndex] === value\n                        ? setData(entity, result)\n                        : result,\n            )\n            return value\n        }\n\n        Object.defineProperty(entity, relation.propertyName, {\n            get: function () {\n                if (\n                    this[resolveIndex] === true ||\n                    this[dataIndex] !== undefined\n                )\n                    // if related data already was loaded then simply return it\n                    return Promise.resolve(this[dataIndex])\n\n                if (this[promiseIndex])\n                    // if related data is loading then return a promise relationLoader loads it\n                    return this[promiseIndex]\n\n                // nothing is loaded yet, load relation data and save it in the model once they are loaded\n                const loader = relationLoader\n                    .load(relation, this, queryRunner)\n                    .then((result) =>\n                        relation.isOneToOne || relation.isManyToOne\n                            ? result.length === 0\n                                ? null\n                                : result[0]\n                            : result,\n                    )\n                return setPromise(this, loader)\n            },\n            set: function (value: any | Promise<any>) {\n                if (value instanceof Promise) {\n                    // if set data is a promise then wait for its resolve and save in the object\n                    setPromise(this, value)\n                } else {\n                    // if its direct data set (non promise, probably not safe-typed)\n                    setData(this, value)\n                }\n            },\n            configurable: true,\n            enumerable: false,\n        })\n    }\n}\n"], "sourceRoot": ".."}