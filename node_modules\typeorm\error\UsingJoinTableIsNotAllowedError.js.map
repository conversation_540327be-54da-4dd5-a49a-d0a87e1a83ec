{"version": 3, "sources": ["../../src/error/UsingJoinTableIsNotAllowedError.ts"], "names": [], "mappings": ";;;AAEA,iDAA6C;AAE7C,MAAa,+BAAgC,SAAQ,2BAAY;IAC7D,YAAY,cAA8B,EAAE,QAA0B;QAClE,KAAK,CACD,sBAAsB,cAAc,CAAC,IAAI,IAAI,QAAQ,CAAC,YAAY,aAAa;YAC3E,GAAG,cAAc,CAAC,IAAI,IAAI,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,YAAY,aAAa;YACzF,+DAA+D,CACtE,CAAA;IACL,CAAC;CACJ;AARD,0EAQC", "file": "UsingJoinTableIsNotAllowedError.js", "sourcesContent": ["import { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { RelationMetadata } from \"../metadata/RelationMetadata\"\nimport { TypeORMError } from \"./TypeORMError\"\n\nexport class UsingJoinTableIsNotAllowedError extends TypeORMError {\n    constructor(entityMetadata: EntityMetadata, relation: RelationMetadata) {\n        super(\n            `Using JoinTable on ${entityMetadata.name}#${relation.propertyName} is wrong. ` +\n                `${entityMetadata.name}#${relation.propertyName} has ${relation.relationType} relation, ` +\n                `however you can use JoinTable only on many-to-many relations.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}