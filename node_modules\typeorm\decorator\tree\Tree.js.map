{"version": 3, "sources": ["../../src/decorator/tree/Tree.ts"], "names": [], "mappings": ";;AAWA,oBAWC;AAtBD,2CAAsD;AAKtD;;;;;GAKG;AACH,SAAgB,IAAI,CAChB,IAAc,EACd,OAA4B;IAE5B,OAAO,UAAU,MAAgB;QAC7B,IAAA,gCAAsB,GAAE,CAAC,KAAK,CAAC,IAAI,CAAC;YAChC,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,IAAI,KAAK,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;SACtC,CAAC,CAAA;IAC1B,CAAC,CAAA;AACL,CAAC", "file": "Tree.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { TreeMetadataArgs } from \"../../metadata-args/TreeMetadataArgs\"\nimport { TreeType } from \"../../metadata/types/TreeTypes\"\nimport { ClosureTreeOptions } from \"../../metadata/types/ClosureTreeOptions\"\n\n/**\n * Marks entity to work like a tree.\n * Tree pattern that will be used for the tree entity should be specified.\n * @TreeParent decorator must be used in tree entities.\n * TreeRepository can be used to manipulate with tree entities.\n */\nexport function Tree(\n    type: TreeType,\n    options?: ClosureTreeOptions,\n): ClassDecorator {\n    return function (target: Function) {\n        getMetadataArgsStorage().trees.push({\n            target: target,\n            type: type,\n            options: type === \"closure-table\" ? options : undefined,\n        } as TreeMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}