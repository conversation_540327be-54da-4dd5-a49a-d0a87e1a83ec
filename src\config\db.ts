import 'reflect-metadata';
import { DataSource } from 'typeorm';
import { User } from '../models/user.model';

// Database configuration based on environment variables
const getDatabaseConfig = () => {
  const dbType = process.env.DB_TYPE || 'mysql'; // Default to MySQL

  const baseConfig = {
    entities: [User],
    synchronize: process.env.NODE_ENV !== 'production', // Auto-sync in development
    logging: process.env.NODE_ENV === 'development',
    migrations: process.env.NODE_ENV === 'production'
      ? ['dist/migrations/*.js']
      : ['src/migrations/*.ts'],
    subscribers: process.env.NODE_ENV === 'production'
      ? ['dist/subscribers/*.js']
      : ['src/subscribers/*.ts'],
  };

  switch (dbType.toLowerCase()) {
    case 'mysql':
    case 'mariadb':
      return {
        type: dbType as 'mysql' | 'mariadb',
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '3306'),
        username: process.env.DB_USERNAME || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'jameela_db',
        charset: 'utf8mb4',
        timezone: '+00:00',
        ...baseConfig,
      };

    case 'postgres':
    case 'postgresql':
      return {
        type: 'postgres' as const,
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '5432'),
        username: process.env.DB_USERNAME || 'postgres',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'jameela_db',
        schema: process.env.DB_SCHEMA || 'public',
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
        ...baseConfig,
      };

    case 'sqlite':
      return {
        type: 'sqlite' as const,
        database: process.env.DB_PATH || './database.sqlite',
        ...baseConfig,
      };

    default:
      throw new Error(`Unsupported database type: ${dbType}`);
  }
};

// Create DataSource instance
export const AppDataSource = new DataSource(getDatabaseConfig());

// Initialize database connection
export const initializeDatabase = async (): Promise<void> => {
  try {
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ Database connection established successfully');

      if (process.env.NODE_ENV === 'development') {
        console.log(`📊 Database: ${AppDataSource.options.type}`);
        if ('host' in AppDataSource.options) {
          console.log(`🌐 Host: ${AppDataSource.options.host}:${AppDataSource.options.port}`);
        }
        if ('database' in AppDataSource.options) {
          console.log(`💾 Database: ${AppDataSource.options.database}`);
        }
      }
    }
  } catch (error) {
    console.error('❌ Error during database initialization:', error);
    throw error;
  }
};

// Close database connection
export const closeDatabase = async (): Promise<void> => {
  try {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('✅ Database connection closed successfully');
    }
  } catch (error) {
    console.error('❌ Error during database closure:', error);
    throw error;
  }
};

// Export default
export default AppDataSource;