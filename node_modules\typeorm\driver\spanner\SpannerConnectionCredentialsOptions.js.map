{"version": 3, "sources": ["../../src/driver/spanner/SpannerConnectionCredentialsOptions.ts"], "names": [], "mappings": "", "file": "SpannerConnectionCredentialsOptions.js", "sourcesContent": ["/**\n * Spanner specific connection credential options.\n */\nexport interface SpannerConnectionCredentialsOptions {\n    /**\n     * Connection url where the connection is performed.\n     */\n    readonly instanceId?: string\n\n    /**\n     * Database host.\n     */\n    readonly projectId?: string\n\n    /**\n     * Database host port.\n     */\n    readonly databaseId?: string\n}\n"], "sourceRoot": "../.."}