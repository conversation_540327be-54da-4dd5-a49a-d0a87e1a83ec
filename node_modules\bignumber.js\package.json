{"name": "bignumber.js", "description": "A library for arbitrary-precision decimal and non-decimal arithmetic", "version": "9.0.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "repository": {"type": "git", "url": "https://github.com/MikeMcl/bignumber.js.git"}, "main": "bignumber", "module": "bignumber.mjs", "browser": "bignumber.js", "types": "bignumber.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": "*"}, "license": "MIT", "scripts": {"test": "node test/test", "build": "uglifyjs bignumber.js --source-map -c -m -o bignumber.min.js"}, "dependencies": {}}