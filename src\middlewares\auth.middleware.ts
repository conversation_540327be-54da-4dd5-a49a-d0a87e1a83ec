import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/auth.service';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: any;
    }
  }
}

export class AuthMiddleware {
  private authService = new AuthService();

  // Middleware to verify JWT token
  public verifyToken = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;

      if (!authHeader) {
        res.status(401).json({
          success: false,
          message: 'Access token is required'
        });
        return;
      }

      const token = authHeader.split(' ')[1]; // Bearer TOKEN

      if (!token) {
        res.status(401).json({
          success: false,
          message: 'Access token is required'
        });
        return;
      }

      // Verify token
      const decoded = this.authService.verifyToken(token);

      // Get user from database
      const user = await this.authService.getUserById(decoded.userId);

      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Invalid token - user not found'
        });
        return;
      }

      // Add user to request object
      req.user = user;
      next();

    } catch (error) {
      console.error('Token verification error:', error);
      res.status(401).json({
        success: false,
        message: 'Invalid or expired token'
      });
    }
  };

  // Middleware to check user roles
  public requireRole = (roles: string[]) => {
    return (req: Request, res: Response, next: NextFunction) => {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      if (!roles.includes(req.user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
      }

      next();
    };
  };

  // Optional authentication (doesn't fail if no token)
  public optionalAuth = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const authHeader = req.headers.authorization;

      if (authHeader) {
        const token = authHeader.split(' ')[1];

        if (token) {
          const decoded = this.authService.verifyToken(token);
          const user = await this.authService.getUserById(decoded.userId);

          if (user) {
            req.user = user;
          }
        }
      }

      next();
    } catch (error) {
      // Continue without authentication
      next();
    }
  };
}

// Export instance
export const authMiddleware = new AuthMiddleware();