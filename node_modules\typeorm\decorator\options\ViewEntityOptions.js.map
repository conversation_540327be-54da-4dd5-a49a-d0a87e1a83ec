{"version": 3, "sources": ["../../src/decorator/options/ViewEntityOptions.ts"], "names": [], "mappings": "", "file": "ViewEntityOptions.js", "sourcesContent": ["/**\n * Describes all entity view's options.\n */\nimport { DataSource, SelectQueryBuilder } from \"../..\"\n\nexport interface ViewEntityOptions {\n    /**\n     * View name.\n     * If not specified then naming strategy will generate view name from class name.\n     */\n    name?: string\n\n    /**\n     * View expression.\n     */\n    expression?: string | ((connection: DataSource) => SelectQueryBuilder<any>)\n\n    /**\n     * Database name. Used in Mysql and Sql Server.\n     */\n    database?: string\n\n    /**\n     * Schema name. Used in Postgres and Sql Server.\n     */\n    schema?: string\n\n    /**\n     * Indicates if schema synchronization is enabled or disabled for this entity.\n     * If it will be set to false then schema sync will and migrations ignore this entity.\n     * By default schema synchronization is enabled for all entities.\n     */\n    synchronize?: boolean\n\n    /**\n     * Indicates if view should be materialized view.\n     * It's supported by Postgres and Oracle.\n     */\n    materialized?: boolean\n\n    /**\n     * View dependencies. In case the view depends on another view it can be listed here\n     * to ensure correct order of view migrations.\n     */\n    dependsOn?: (Function | string)[]\n}\n"], "sourceRoot": "../.."}