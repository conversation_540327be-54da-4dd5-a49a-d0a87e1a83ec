{"version": 3, "sources": ["../../src/schema-builder/options/TableOptions.ts"], "names": [], "mappings": "", "file": "TableOptions.js", "sourcesContent": ["import { TableColumnOptions } from \"./TableColumnOptions\"\nimport { TableIndexOptions } from \"./TableIndexOptions\"\nimport { TableForeignKeyOptions } from \"./TableForeignKeyOptions\"\nimport { TableUniqueOptions } from \"./TableUniqueOptions\"\nimport { TableCheckOptions } from \"./TableCheckOptions\"\nimport { TableExclusionOptions } from \"./TableExclusionOptions\"\n\n/**\n * Table options.\n */\nexport interface TableOptions {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Table schema.\n     */\n    schema?: string\n\n    /**\n     * Table database.\n     */\n    database?: string\n\n    /**\n     * Table name.\n     */\n    name: string\n\n    /**\n     * Table columns.\n     */\n    columns?: TableColumnOptions[]\n\n    /**\n     * Table indices.\n     */\n    indices?: TableIndexOptions[]\n\n    /**\n     * Table foreign keys.\n     */\n    foreignKeys?: TableForeignKeyOptions[]\n\n    /**\n     * Table unique constraints.\n     */\n    uniques?: TableUniqueOptions[]\n\n    /**\n     * Table check constraints.\n     */\n    checks?: TableCheckOptions[]\n\n    /**\n     * Table check constraints.\n     */\n    exclusions?: TableExclusionOptions[]\n\n    /**\n     * Indicates if table was just created.\n     * This is needed, for example to check if we need to skip primary keys creation\n     * for new tables.\n     */\n    justCreated?: boolean\n\n    /**\n     * Enables Sqlite \"WITHOUT ROWID\" modifier for the \"CREATE TABLE\" statement\n     */\n    withoutRowid?: boolean\n\n    /**\n     * Table engine.\n     */\n    engine?: string\n\n    /**\n     * Table comment. Not supported by all database types.\n     */\n    comment?: string\n}\n"], "sourceRoot": "../.."}