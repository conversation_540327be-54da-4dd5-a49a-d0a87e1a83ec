import { Request, Response } from 'express';
import { AuthService } from '../services/auth.service';
import { ValidationUtils } from '../utils/validation';

const authService = new AuthService();

// User registration/signup
export const signup = async (req: Request, res: Response): Promise<void> => {
  try {
    // Validate input data
    const validation = ValidationUtils.validateSignupData(req.body);
    if (!validation.isValid) {
      res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors
      });
      return;
    }

    // Sanitize input
    const signupData = {
      username: ValidationUtils.sanitizeString(req.body.username),
      email: ValidationUtils.sanitizeString(req.body.email.toLowerCase()),
      password: req.body.password,
      phone: req.body.phone ? ValidationUtils.sanitizeString(req.body.phone) : undefined,
      role: req.body.role || 'Student'
    };

    // Call auth service
    const result = await authService.signup(signupData);

    if (result.success) {
      res.status(201).json(result);
    } else {
      res.status(400).json(result);
    }

  } catch (error) {
    console.error('Signup controller error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// User login
export const login = async (req: Request, res: Response): Promise<void> => {
  try {
    // Validate input data
    const validation = ValidationUtils.validateLoginData(req.body);
    if (!validation.isValid) {
      res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors
      });
      return;
    }

    // Sanitize input
    const loginData = {
      email: ValidationUtils.sanitizeString(req.body.email.toLowerCase()),
      password: req.body.password
    };

    // Call auth service
    const result = await authService.login(loginData);

    if (result.success) {
      res.status(200).json(result);
    } else {
      res.status(401).json(result);
    }

  } catch (error) {
    console.error('Login controller error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get current user profile
export const getProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const user = req.user;

    if (!user) {
      res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
      return;
    }

    res.status(200).json({
      success: true,
      message: 'Profile retrieved successfully',
      user: user.toJSON ? user.toJSON() : user
    });

  } catch (error) {
    console.error('Get profile controller error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Update user profile
export const updateProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
      return;
    }

    // Validate input data
    const validation = ValidationUtils.validateUpdateProfileData(req.body);
    if (!validation.isValid) {
      res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors
      });
      return;
    }

    // Sanitize input
    const updateData: any = {};
    if (req.body.username) {
      updateData.username = ValidationUtils.sanitizeString(req.body.username);
    }
    if (req.body.phone) {
      updateData.phone = ValidationUtils.sanitizeString(req.body.phone);
    }
    if (req.body.role) {
      updateData.role = req.body.role;
    }

    // Call auth service
    const result = await authService.updateProfile(userId, updateData);

    if (result.success) {
      res.status(200).json(result);
    } else {
      res.status(400).json(result);
    }

  } catch (error) {
    console.error('Update profile controller error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Change password
export const changePassword = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
      return;
    }

    // Validate input data
    const validation = ValidationUtils.validateChangePasswordData(req.body);
    if (!validation.isValid) {
      res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors
      });
      return;
    }

    // Call auth service
    const result = await authService.changePassword(
      userId,
      req.body.currentPassword,
      req.body.newPassword
    );

    if (result.success) {
      res.status(200).json(result);
    } else {
      res.status(400).json(result);
    }

  } catch (error) {
    console.error('Change password controller error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Logout
export const logout = async (req: Request, res: Response): Promise<void> => {
  try {
    // In a JWT-based system, logout is typically handled client-side
    // by removing the token. Here we just acknowledge the logout.

    res.status(200).json({
      success: true,
      message: 'Logged out successfully'
    });

  } catch (error) {
    console.error('Logout controller error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// For backward compatibility
export const registerUser = signup;
