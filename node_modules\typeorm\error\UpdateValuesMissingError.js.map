{"version": 3, "sources": ["../../src/error/UpdateValuesMissingError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C,MAAa,wBAAyB,SAAQ,2BAAY;IACtD;QACI,KAAK,CACD,yHAAyH,CAC5H,CAAA;IACL,CAAC;CACJ;AAND,4DAMC", "file": "UpdateValuesMissingError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\nexport class UpdateValuesMissingError extends TypeORMError {\n    constructor() {\n        super(\n            `Cannot perform update query because update values are not defined. Call \"qb.set(...)\" method to specify updated values.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}