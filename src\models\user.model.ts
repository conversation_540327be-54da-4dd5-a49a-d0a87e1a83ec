import { <PERSON><PERSON><PERSON>, PrimaryColumn, Column, BaseEntity, BeforeInsert, BeforeUpdate } from 'typeorm';
import * as bcrypt from 'bcryptjs';

@Entity('users')
export class User extends BaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 10 })
  id!: string;

  @Column({ type: 'varchar', length: 20 })
  username!: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  email!: string;

  @Column({ type: 'varchar', length: 100, default: "" })
  phone!: string;

  @Column({ type: 'boolean', default: false })
  phone_nb_verified!: boolean;

  @Column({ type: 'varchar', length: 255 })
  password!: string;

  @Column({ type: 'varchar', length: 50, default: "Student" })
  role!: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  creation_date!: Date;

  @Column({ type: 'longblob', nullable: true })
  profile_picture?: Buffer;

  // Password hashing before insert
  @BeforeInsert()
  async hashPassword() {
    if (this.password) {
      const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS || '12');
      this.password = await bcrypt.hash(this.password, saltRounds);
    }
  }

  // Password hashing before update (if password is changed)
  @BeforeUpdate()
  async hashPasswordOnUpdate() {
    if (this.password) {
      const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS || '12');
      this.password = await bcrypt.hash(this.password, saltRounds);
    }
  }

  // Method to compare passwords
  async comparePassword(candidatePassword: string): Promise<boolean> {
    return bcrypt.compare(candidatePassword, this.password);
  }

  // Method to generate user ID (you can customize this logic)
  static generateUserId(): string {
    return Math.random().toString(36).substring(2, 12).toUpperCase();
  }

  // Method to exclude password from JSON response
  toJSON() {
    const { password, ...userWithoutPassword } = this;
    return userWithoutPassword;
  }
}
