{"version": 3, "sources": ["../../src/query-builder/SoftDeleteQueryBuilder.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAO7C,wDAAoD;AACpD,sGAAkG;AAClG,mFAA+E;AAE/E,4FAAwF;AACxF,wFAAoF;AACpF,gFAA4E;AAC5E,oCAAuC;AACvC,uDAAmD;AACnD,6DAAyD;AAEzD;;GAEG;AACH,MAAa,sBACT,SAAQ,2BAAoB;IAK5B,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACI,wBAAwD,EACxD,WAAyB;QAEzB,KAAK,CAAC,wBAA+B,EAAE,WAAW,CAAC,CAAA;QAV9C,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAA;QAWzD,IAAI,CAAC,aAAa,CAAC,yBAAyB,GAAG,KAAK,CAAA;IACxD,CAAC;IAED,4EAA4E;IAC5E,6BAA6B;IAC7B,4EAA4E;IAE5E;;OAEG;IACH,QAAQ;QACJ,IAAI,GAAG,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QACvC,GAAG,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAA;QACjC,GAAG,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAA;QACrC,GAAG,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;QACnC,OAAO,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC5C,IAAI,sBAAsB,GAAY,KAAK,CAAA;QAE3C,IAAI,CAAC;YACD,sCAAsC;YACtC,IACI,IAAI,CAAC,aAAa,CAAC,cAAc,KAAK,IAAI;gBAC1C,WAAW,CAAC,mBAAmB,KAAK,KAAK,EAC3C,CAAC;gBACC,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;gBACpC,sBAAsB,GAAG,IAAI,CAAA;YACjC,CAAC;YAED,2EAA2E;YAC3E,IACI,IAAI,CAAC,aAAa,CAAC,aAAa,KAAK,IAAI;gBACzC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW,EAC3C,CAAC;gBACC,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,KAAK,aAAa;oBAC9C,MAAM,WAAW,CAAC,WAAW,CAAC,SAAS,CACnC,kBAAkB,EAClB,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CACzC,CAAA;qBACA,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,KAAK,SAAS;oBAC/C,MAAM,WAAW,CAAC,WAAW,CAAC,SAAS,CACnC,eAAe,EACf,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CACzC,CAAA;YACT,CAAC;YAED,yFAAyF;YACzF,MAAM,6BAA6B,GAC/B,IAAI,6DAA6B,CAC7B,WAAW,EACX,IAAI,CAAC,aAAa,CACrB,CAAA;YACL,IACI,IAAI,CAAC,aAAa,CAAC,YAAY,KAAK,IAAI;gBACxC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW;gBACzC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAC7C,CAAC;gBACC,IAAI,CAAC,aAAa,CAAC,qBAAqB;oBACpC,6BAA6B,CAAC,+BAA+B,EAAE,CAAA;YACvE,CAAC;YAED,uBAAuB;YACvB,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAA;YAEtD,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;YAClE,MAAM,YAAY,GAAG,2BAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAEnD,qIAAqI;YACrI,IACI,IAAI,CAAC,aAAa,CAAC,YAAY,KAAK,IAAI;gBACxC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW;gBACzC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAC7C,CAAC;gBACC,MAAM,6BAA6B,CAAC,MAAM,CACtC,YAAY,EACZ,IAAI,CAAC,aAAa,CAAC,aAAa,CACnC,CAAA;YACL,CAAC;YAED,0EAA0E;YAC1E,IACI,IAAI,CAAC,aAAa,CAAC,aAAa,KAAK,IAAI;gBACzC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW,EAC3C,CAAC;gBACC,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,KAAK,aAAa;oBAC9C,MAAM,WAAW,CAAC,WAAW,CAAC,SAAS,CACnC,iBAAiB,EACjB,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CACzC,CAAA;qBACA,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,KAAK,SAAS;oBAC/C,MAAM,WAAW,CAAC,WAAW,CAAC,SAAS,CACnC,cAAc,EACd,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CACzC,CAAA;YACT,CAAC;YAED,qCAAqC;YACrC,IAAI,sBAAsB;gBAAE,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAA;YAEjE,OAAO,YAAY,CAAA;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,wCAAwC;YACxC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACD,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAA;gBAC3C,CAAC;gBAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC9B,CAAC;YACD,MAAM,KAAK,CAAA;QACf,CAAC;gBAAS,CAAC;YACP,IAAI,WAAW,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnC,wCAAwC;gBACxC,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;YAC/B,CAAC;QACL,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;;OAGG;IACH,IAAI,CACA,YAA6B,EAC7B,SAAkB;QAElB,YAAY,GAAG,iCAAe,CAAC,cAAc,CAAC,YAAY,CAAC;YACvD,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI;YAC3B,CAAC,CAAC,YAAY,CAAA;QAClB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,SAAS,CAAC,CAAA;QAC/D,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;QAC1C,OAAO,IAAwC,CAAA;IACnD,CAAC;IAED;;;;;OAKG;IACH,KAAK,CACD,KAKqB,EACrB,UAA0B;QAE1B,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,CAAA,CAAC,oFAAoF;QACnH,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC/C,IAAI,SAAS;YACT,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG;gBACxB,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE;aAC3C,CAAA;QACL,IAAI,UAAU;YAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;OAGG;IACH,QAAQ,CACJ,KAKqB,EACrB,UAA0B;QAE1B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;YAC3B,IAAI,EAAE,KAAK;YACX,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;SAC3C,CAAC,CAAA;QACF,IAAI,UAAU;YAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;OAGG;IACH,OAAO,CACH,KAKqB,EACrB,UAA0B;QAE1B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;YAC3B,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;SAC3C,CAAC,CAAA;QACF,IAAI,UAAU;YAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,GAAgB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAA;IACvD,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,GAAgB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAA;IAC1D,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,GAAgB;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAA;IACzD,CAAC;IAkBD;;OAEG;IACH,MAAM,CAAC,MAAyB;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;IACjC,CAAC;IAmBD;;OAEG;IACH,SAAS,CAAC,SAA4B;QAClC,mDAAmD;QACnD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,uBAAuB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,yEAAmC,EAAE,CAAA;QACnD,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,SAAS,CAAA;QACxC,OAAO,IAAI,CAAA;IACf,CAAC;IA6BD;;;;OAIG;IACH,OAAO,CACH,IAAgC,EAChC,QAAwB,KAAK,EAC7B,KAAoC;QAEpC,IAAI,IAAI,EAAE,CAAC;YACP,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC3B,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAwB,CAAA;YAC1D,CAAC;iBAAM,CAAC;gBACJ,IAAI,KAAK,EAAE,CAAC;oBACR,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG;wBAC1B,CAAC,IAAc,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;qBACrC,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,EAAE,CAAC,IAAc,CAAC,EAAE,KAAK,EAAE,CAAA;gBAC7D,CAAC;YACL,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,EAAE,CAAA;QACpC,CAAC;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,UAAU,CACN,IAAY,EACZ,QAAwB,KAAK,EAC7B,KAAoC;QAEpC,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAA;QACxD,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAA;QAC7C,CAAC;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAc;QAChB,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,KAAK,CAAA;QAChC,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,WAAW,CAAC,MAAyB;QACjC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW;YAC1C,MAAM,IAAI,oBAAY,CAClB,iFAAiF,CACpF,CAAA;QAEL,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,CAAA;QAC9B,MAAM,QAAQ,GAAa,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;QACpE,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACxB,MAAM,WAAW,GACb,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;YACjE,IAAI,CAAC,WAAW;gBACZ,MAAM,IAAI,oBAAY,CAClB,kEAAkE,CACrE,CAAA;YAEL,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;QAClC,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG,QAAQ,CAAA;QAC3C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,YAAY,CAAC,OAAgB;QACzB,IAAI,CAAC,aAAa,CAAC,YAAY,GAAG,OAAO,CAAA;QACzC,OAAO,IAAI,CAAA;IACf,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,sBAAsB;QAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW;YACtD,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ;YACxC,CAAC,CAAC,SAAS,CAAA;QACf,IAAI,CAAC,QAAQ;YACT,MAAM,IAAI,oBAAY,CAClB,mDAAmD,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,CACrF,CAAA;QACL,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;YAC7B,MAAM,IAAI,2DAA4B,CAAC,QAAQ,CAAC,CAAA;QACpD,CAAC;QAED,2CAA2C;QAC3C,MAAM,qBAAqB,GAAa,EAAE,CAAA;QAE1C,QAAQ,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;YACnC,KAAK,aAAa;gBACd,qBAAqB,CAAC,IAAI,CACtB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CAAC;oBAC/C,sBAAsB,CAC7B,CAAA;gBACD,MAAK;YACT,KAAK,SAAS;gBACV,qBAAqB,CAAC,IAAI,CACtB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CAAC;oBAC/C,SAAS,CAChB,CAAA;gBACD,MAAK;YACT;gBACI,MAAM,IAAI,oBAAY,CAClB,kDAAkD,CACrD,CAAA;QACT,CAAC;QACD,IAAI,QAAQ,CAAC,aAAa;YACtB,qBAAqB,CAAC,IAAI,CACtB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,YAAY,CAAC;gBAC5C,KAAK;gBACL,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,YAAY,CAAC;gBAChD,MAAM,CACb,CAAA;QACL,IAAI,QAAQ,CAAC,gBAAgB;YACzB,qBAAqB,CAAC,IAAI,CACtB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CAAC;gBAC/C,sBAAsB,CAC7B,CAAA,CAAC,gFAAgF;QAEtF,IAAI,qBAAqB,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACpC,MAAM,IAAI,mDAAwB,EAAE,CAAA;QACxC,CAAC;QAED,iDAAiD;QACjD,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAA;QACpD,MAAM,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAA;QAEpE,IAAI,mBAAmB,KAAK,EAAE,EAAE,CAAC;YAC7B,OAAO,UAAU,IAAI,CAAC,YAAY,CAC9B,IAAI,CAAC,gBAAgB,EAAE,CAC1B,QAAQ,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,eAAe,EAAE,CAAA,CAAC,uDAAuD;QACzH,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClD,OAAO,UAAU,IAAI,CAAC,YAAY,CAC9B,IAAI,CAAC,gBAAgB,EAAE,CAC1B,QAAQ,qBAAqB,CAAC,IAAI,CAC/B,IAAI,CACP,WAAW,mBAAmB,GAAG,eAAe,EAAE,CAAA;QACvD,CAAC;QACD,OAAO,UAAU,IAAI,CAAC,YAAY,CAC9B,IAAI,CAAC,gBAAgB,EAAE,CAC1B,QAAQ,qBAAqB,CAAC,IAAI,CAC/B,IAAI,CACP,GAAG,eAAe,cAAc,mBAAmB,EAAE,CAAA;IAC1D,CAAC;IAED;;OAEG;IACO,uBAAuB;QAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAA;QAC5C,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC;YAChC,OAAO,CACH,YAAY;gBACZ,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;qBAChB,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;oBAChB,IAAI,OAAO,QAAQ,CAAC,UAAU,CAAC,KAAK,QAAQ,EAAE,CAAC;wBAC3C,OAAO,CACH,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC;4BACrC,GAAG;4BACH,QAAQ,CAAC,UAAU,CAAC,CACvB,CAAA;oBACL,CAAC;yBAAM,CAAC;wBACJ,OAAO,CACH,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC;4BACrC,GAAG;4BACF,QAAQ,CAAC,UAAU,CAAS,CAAC,KAAK;4BACnC,GAAG;4BACF,QAAQ,CAAC,UAAU,CAAS,CAAC,KAAK,CACtC,CAAA;oBACL,CAAC;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,IAAI,CAAC,CAClB,CAAA;QAEL,OAAO,EAAE,CAAA;IACb,CAAC;IAED;;OAEG;IACO,qBAAqB;QAC3B,MAAM,KAAK,GAAuB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAA;QAE1D,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,yBAAW,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpD,OAAO,SAAS,GAAG,KAAK,CAAA;YAC5B,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,+DAA8B,EAAE,CAAA;YAC9C,CAAC;QACL,CAAC;QAED,OAAO,EAAE,CAAA;IACb,CAAC;CACJ;AA/hBD,wDA+hBC", "file": "SoftDeleteQueryBuilder.js", "sourcesContent": ["import { QueryBuilder } from \"./QueryBuilder\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { EntityTarget } from \"../common/EntityTarget\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { WhereExpressionBuilder } from \"./WhereExpressionBuilder\"\nimport { Brackets } from \"./Brackets\"\nimport { UpdateResult } from \"./result/UpdateResult\"\nimport { ReturningStatementNotSupportedError } from \"../error/ReturningStatementNotSupportedError\"\nimport { ReturningResultsEntityUpdator } from \"./ReturningResultsEntityUpdator\"\nimport { OrderByCondition } from \"../find-options/OrderByCondition\"\nimport { LimitOnUpdateNotSupportedError } from \"../error/LimitOnUpdateNotSupportedError\"\nimport { MissingDeleteDateColumnError } from \"../error/MissingDeleteDateColumnError\"\nimport { UpdateValuesMissingError } from \"../error/UpdateValuesMissingError\"\nimport { TypeORMError } from \"../error\"\nimport { DriverUtils } from \"../driver/DriverUtils\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\n\n/**\n * Allows to build complex sql queries in a fashion way and execute those queries.\n */\nexport class SoftDeleteQueryBuilder<Entity extends ObjectLiteral>\n    extends QueryBuilder<Entity>\n    implements WhereExpressionBuilder\n{\n    readonly \"@instanceof\" = Symbol.for(\"SoftDeleteQueryBuilder\")\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        connectionOrQueryBuilder: DataSource | QueryBuilder<any>,\n        queryRunner?: QueryRunner,\n    ) {\n        super(connectionOrQueryBuilder as any, queryRunner)\n        this.expressionMap.aliasNamePrefixingEnabled = false\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Gets generated SQL query without parameters being replaced.\n     */\n    getQuery(): string {\n        let sql = this.createUpdateExpression()\n        sql += this.createCteExpression()\n        sql += this.createOrderByExpression()\n        sql += this.createLimitExpression()\n        return this.replacePropertyNamesForTheWholeQuery(sql.trim())\n    }\n\n    /**\n     * Executes sql generated by query builder and returns raw database results.\n     */\n    async execute(): Promise<UpdateResult> {\n        const queryRunner = this.obtainQueryRunner()\n        let transactionStartedByUs: boolean = false\n\n        try {\n            // start transaction if it was enabled\n            if (\n                this.expressionMap.useTransaction === true &&\n                queryRunner.isTransactionActive === false\n            ) {\n                await queryRunner.startTransaction()\n                transactionStartedByUs = true\n            }\n\n            // call before soft remove and recover methods in listeners and subscribers\n            if (\n                this.expressionMap.callListeners === true &&\n                this.expressionMap.mainAlias!.hasMetadata\n            ) {\n                if (this.expressionMap.queryType === \"soft-delete\")\n                    await queryRunner.broadcaster.broadcast(\n                        \"BeforeSoftRemove\",\n                        this.expressionMap.mainAlias!.metadata,\n                    )\n                else if (this.expressionMap.queryType === \"restore\")\n                    await queryRunner.broadcaster.broadcast(\n                        \"BeforeRecover\",\n                        this.expressionMap.mainAlias!.metadata,\n                    )\n            }\n\n            // if update entity mode is enabled we may need extra columns for the returning statement\n            const returningResultsEntityUpdator =\n                new ReturningResultsEntityUpdator(\n                    queryRunner,\n                    this.expressionMap,\n                )\n            if (\n                this.expressionMap.updateEntity === true &&\n                this.expressionMap.mainAlias!.hasMetadata &&\n                this.expressionMap.whereEntities.length > 0\n            ) {\n                this.expressionMap.extraReturningColumns =\n                    returningResultsEntityUpdator.getSoftDeletionReturningColumns()\n            }\n\n            // execute update query\n            const [sql, parameters] = this.getQueryAndParameters()\n\n            const queryResult = await queryRunner.query(sql, parameters, true)\n            const updateResult = UpdateResult.from(queryResult)\n\n            // if we are updating entities and entity updation is enabled we must update some of entity columns (like version, update date, etc.)\n            if (\n                this.expressionMap.updateEntity === true &&\n                this.expressionMap.mainAlias!.hasMetadata &&\n                this.expressionMap.whereEntities.length > 0\n            ) {\n                await returningResultsEntityUpdator.update(\n                    updateResult,\n                    this.expressionMap.whereEntities,\n                )\n            }\n\n            // call after soft remove and recover methods in listeners and subscribers\n            if (\n                this.expressionMap.callListeners === true &&\n                this.expressionMap.mainAlias!.hasMetadata\n            ) {\n                if (this.expressionMap.queryType === \"soft-delete\")\n                    await queryRunner.broadcaster.broadcast(\n                        \"AfterSoftRemove\",\n                        this.expressionMap.mainAlias!.metadata,\n                    )\n                else if (this.expressionMap.queryType === \"restore\")\n                    await queryRunner.broadcaster.broadcast(\n                        \"AfterRecover\",\n                        this.expressionMap.mainAlias!.metadata,\n                    )\n            }\n\n            // close transaction if we started it\n            if (transactionStartedByUs) await queryRunner.commitTransaction()\n\n            return updateResult\n        } catch (error) {\n            // rollback transaction if we started it\n            if (transactionStartedByUs) {\n                try {\n                    await queryRunner.rollbackTransaction()\n                } catch (rollbackError) {}\n            }\n            throw error\n        } finally {\n            if (queryRunner !== this.queryRunner) {\n                // means we created our own query runner\n                await queryRunner.release()\n            }\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Specifies FROM which entity's table select/update/delete/soft-delete will be executed.\n     * Also sets a main string alias of the selection data.\n     */\n    from<T extends ObjectLiteral>(\n        entityTarget: EntityTarget<T>,\n        aliasName?: string,\n    ): SoftDeleteQueryBuilder<T> {\n        entityTarget = InstanceChecker.isEntitySchema(entityTarget)\n            ? entityTarget.options.name\n            : entityTarget\n        const mainAlias = this.createFromAlias(entityTarget, aliasName)\n        this.expressionMap.setMainAlias(mainAlias)\n        return this as any as SoftDeleteQueryBuilder<T>\n    }\n\n    /**\n     * Sets WHERE condition in the query builder.\n     * If you had previously WHERE expression defined,\n     * calling this function will override previously set WHERE conditions.\n     * Additionally you can add parameters used in where expression.\n     */\n    where(\n        where:\n            | string\n            | ((qb: this) => string)\n            | Brackets\n            | ObjectLiteral\n            | ObjectLiteral[],\n        parameters?: ObjectLiteral,\n    ): this {\n        this.expressionMap.wheres = [] // don't move this block below since computeWhereParameter can add where expressions\n        const condition = this.getWhereCondition(where)\n        if (condition)\n            this.expressionMap.wheres = [\n                { type: \"simple\", condition: condition },\n            ]\n        if (parameters) this.setParameters(parameters)\n        return this\n    }\n\n    /**\n     * Adds new AND WHERE condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    andWhere(\n        where:\n            | string\n            | ((qb: this) => string)\n            | Brackets\n            | ObjectLiteral\n            | ObjectLiteral[],\n        parameters?: ObjectLiteral,\n    ): this {\n        this.expressionMap.wheres.push({\n            type: \"and\",\n            condition: this.getWhereCondition(where),\n        })\n        if (parameters) this.setParameters(parameters)\n        return this\n    }\n\n    /**\n     * Adds new OR WHERE condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    orWhere(\n        where:\n            | string\n            | ((qb: this) => string)\n            | Brackets\n            | ObjectLiteral\n            | ObjectLiteral[],\n        parameters?: ObjectLiteral,\n    ): this {\n        this.expressionMap.wheres.push({\n            type: \"or\",\n            condition: this.getWhereCondition(where),\n        })\n        if (parameters) this.setParameters(parameters)\n        return this\n    }\n\n    /**\n     * Adds new AND WHERE with conditions for the given ids.\n     */\n    whereInIds(ids: any | any[]): this {\n        return this.where(this.getWhereInIdsCondition(ids))\n    }\n\n    /**\n     * Adds new AND WHERE with conditions for the given ids.\n     */\n    andWhereInIds(ids: any | any[]): this {\n        return this.andWhere(this.getWhereInIdsCondition(ids))\n    }\n\n    /**\n     * Adds new OR WHERE with conditions for the given ids.\n     */\n    orWhereInIds(ids: any | any[]): this {\n        return this.orWhere(this.getWhereInIdsCondition(ids))\n    }\n    /**\n     * Optional returning/output clause.\n     * This will return given column values.\n     */\n    output(columns: string[]): this\n\n    /**\n     * Optional returning/output clause.\n     * Returning is a SQL string containing returning statement.\n     */\n    output(output: string): this\n\n    /**\n     * Optional returning/output clause.\n     */\n    output(output: string | string[]): this\n\n    /**\n     * Optional returning/output clause.\n     */\n    output(output: string | string[]): this {\n        return this.returning(output)\n    }\n\n    /**\n     * Optional returning/output clause.\n     * This will return given column values.\n     */\n    returning(columns: string[]): this\n\n    /**\n     * Optional returning/output clause.\n     * Returning is a SQL string containing returning statement.\n     */\n    returning(returning: string): this\n\n    /**\n     * Optional returning/output clause.\n     */\n    returning(returning: string | string[]): this\n\n    /**\n     * Optional returning/output clause.\n     */\n    returning(returning: string | string[]): this {\n        // not all databases support returning/output cause\n        if (!this.connection.driver.isReturningSqlSupported(\"update\")) {\n            throw new ReturningStatementNotSupportedError()\n        }\n\n        this.expressionMap.returning = returning\n        return this\n    }\n\n    /**\n     * Sets ORDER BY condition in the query builder.\n     * If you had previously ORDER BY expression defined,\n     * calling this function will override previously set ORDER BY conditions.\n     *\n     * Calling order by without order set will remove all previously set order bys.\n     */\n    orderBy(): this\n\n    /**\n     * Sets ORDER BY condition in the query builder.\n     * If you had previously ORDER BY expression defined,\n     * calling this function will override previously set ORDER BY conditions.\n     */\n    orderBy(\n        sort: string,\n        order?: \"ASC\" | \"DESC\",\n        nulls?: \"NULLS FIRST\" | \"NULLS LAST\",\n    ): this\n\n    /**\n     * Sets ORDER BY condition in the query builder.\n     * If you had previously ORDER BY expression defined,\n     * calling this function will override previously set ORDER BY conditions.\n     */\n    orderBy(order: OrderByCondition): this\n\n    /**\n     * Sets ORDER BY condition in the query builder.\n     * If you had previously ORDER BY expression defined,\n     * calling this function will override previously set ORDER BY conditions.\n     */\n    orderBy(\n        sort?: string | OrderByCondition,\n        order: \"ASC\" | \"DESC\" = \"ASC\",\n        nulls?: \"NULLS FIRST\" | \"NULLS LAST\",\n    ): this {\n        if (sort) {\n            if (typeof sort === \"object\") {\n                this.expressionMap.orderBys = sort as OrderByCondition\n            } else {\n                if (nulls) {\n                    this.expressionMap.orderBys = {\n                        [sort as string]: { order, nulls },\n                    }\n                } else {\n                    this.expressionMap.orderBys = { [sort as string]: order }\n                }\n            }\n        } else {\n            this.expressionMap.orderBys = {}\n        }\n        return this\n    }\n\n    /**\n     * Adds ORDER BY condition in the query builder.\n     */\n    addOrderBy(\n        sort: string,\n        order: \"ASC\" | \"DESC\" = \"ASC\",\n        nulls?: \"NULLS FIRST\" | \"NULLS LAST\",\n    ): this {\n        if (nulls) {\n            this.expressionMap.orderBys[sort] = { order, nulls }\n        } else {\n            this.expressionMap.orderBys[sort] = order\n        }\n        return this\n    }\n\n    /**\n     * Sets LIMIT - maximum number of rows to be selected.\n     */\n    limit(limit?: number): this {\n        this.expressionMap.limit = limit\n        return this\n    }\n\n    /**\n     * Indicates if entity must be updated after update operation.\n     * This may produce extra query or use RETURNING / OUTPUT statement (depend on database).\n     * Enabled by default.\n     */\n    whereEntity(entity: Entity | Entity[]): this {\n        if (!this.expressionMap.mainAlias!.hasMetadata)\n            throw new TypeORMError(\n                `.whereEntity method can only be used on queries which update real entity table.`,\n            )\n\n        this.expressionMap.wheres = []\n        const entities: Entity[] = Array.isArray(entity) ? entity : [entity]\n        entities.forEach((entity) => {\n            const entityIdMap =\n                this.expressionMap.mainAlias!.metadata.getEntityIdMap(entity)\n            if (!entityIdMap)\n                throw new TypeORMError(\n                    `Provided entity does not have ids set, cannot perform operation.`,\n                )\n\n            this.orWhereInIds(entityIdMap)\n        })\n\n        this.expressionMap.whereEntities = entities\n        return this\n    }\n\n    /**\n     * Indicates if entity must be updated after update operation.\n     * This may produce extra query or use RETURNING / OUTPUT statement (depend on database).\n     * Enabled by default.\n     */\n    updateEntity(enabled: boolean): this {\n        this.expressionMap.updateEntity = enabled\n        return this\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates UPDATE express used to perform insert query.\n     */\n    protected createUpdateExpression() {\n        const metadata = this.expressionMap.mainAlias!.hasMetadata\n            ? this.expressionMap.mainAlias!.metadata\n            : undefined\n        if (!metadata)\n            throw new TypeORMError(\n                `Cannot get entity metadata for the given alias \"${this.expressionMap.mainAlias}\"`,\n            )\n        if (!metadata.deleteDateColumn) {\n            throw new MissingDeleteDateColumnError(metadata)\n        }\n\n        // prepare columns and values to be updated\n        const updateColumnAndValues: string[] = []\n\n        switch (this.expressionMap.queryType) {\n            case \"soft-delete\":\n                updateColumnAndValues.push(\n                    this.escape(metadata.deleteDateColumn.databaseName) +\n                        \" = CURRENT_TIMESTAMP\",\n                )\n                break\n            case \"restore\":\n                updateColumnAndValues.push(\n                    this.escape(metadata.deleteDateColumn.databaseName) +\n                        \" = NULL\",\n                )\n                break\n            default:\n                throw new TypeORMError(\n                    `The queryType must be \"soft-delete\" or \"restore\"`,\n                )\n        }\n        if (metadata.versionColumn)\n            updateColumnAndValues.push(\n                this.escape(metadata.versionColumn.databaseName) +\n                    \" = \" +\n                    this.escape(metadata.versionColumn.databaseName) +\n                    \" + 1\",\n            )\n        if (metadata.updateDateColumn)\n            updateColumnAndValues.push(\n                this.escape(metadata.updateDateColumn.databaseName) +\n                    \" = CURRENT_TIMESTAMP\",\n            ) // todo: fix issue with CURRENT_TIMESTAMP(6) being used, can \"DEFAULT\" be used?!\n\n        if (updateColumnAndValues.length <= 0) {\n            throw new UpdateValuesMissingError()\n        }\n\n        // get a table name and all column database names\n        const whereExpression = this.createWhereExpression()\n        const returningExpression = this.createReturningExpression(\"update\")\n\n        if (returningExpression === \"\") {\n            return `UPDATE ${this.getTableName(\n                this.getMainTableName(),\n            )} SET ${updateColumnAndValues.join(\", \")}${whereExpression}` // todo: how do we replace aliases in where to nothing?\n        }\n        if (this.connection.driver.options.type === \"mssql\") {\n            return `UPDATE ${this.getTableName(\n                this.getMainTableName(),\n            )} SET ${updateColumnAndValues.join(\n                \", \",\n            )} OUTPUT ${returningExpression}${whereExpression}`\n        }\n        return `UPDATE ${this.getTableName(\n            this.getMainTableName(),\n        )} SET ${updateColumnAndValues.join(\n            \", \",\n        )}${whereExpression} RETURNING ${returningExpression}`\n    }\n\n    /**\n     * Creates \"ORDER BY\" part of SQL query.\n     */\n    protected createOrderByExpression() {\n        const orderBys = this.expressionMap.orderBys\n        if (Object.keys(orderBys).length > 0)\n            return (\n                \" ORDER BY \" +\n                Object.keys(orderBys)\n                    .map((columnName) => {\n                        if (typeof orderBys[columnName] === \"string\") {\n                            return (\n                                this.replacePropertyNames(columnName) +\n                                \" \" +\n                                orderBys[columnName]\n                            )\n                        } else {\n                            return (\n                                this.replacePropertyNames(columnName) +\n                                \" \" +\n                                (orderBys[columnName] as any).order +\n                                \" \" +\n                                (orderBys[columnName] as any).nulls\n                            )\n                        }\n                    })\n                    .join(\", \")\n            )\n\n        return \"\"\n    }\n\n    /**\n     * Creates \"LIMIT\" parts of SQL query.\n     */\n    protected createLimitExpression(): string {\n        const limit: number | undefined = this.expressionMap.limit\n\n        if (limit) {\n            if (DriverUtils.isMySQLFamily(this.connection.driver)) {\n                return \" LIMIT \" + limit\n            } else {\n                throw new LimitOnUpdateNotSupportedError()\n            }\n        }\n\n        return \"\"\n    }\n}\n"], "sourceRoot": ".."}