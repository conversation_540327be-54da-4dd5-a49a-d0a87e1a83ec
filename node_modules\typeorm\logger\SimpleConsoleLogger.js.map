{"version": 3, "sources": ["../../src/logger/SimpleConsoleLogger.ts"], "names": [], "mappings": ";;;AAAA,qDAAiD;AAIjD;;;GAGG;AACH,MAAa,mBAAoB,SAAQ,+BAAc;IACnD;;OAEG;IACO,QAAQ,CACd,KAAe,EACf,UAAqC,EACrC,WAAyB;QAEzB,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE;YACjD,YAAY,EAAE,KAAK;SACtB,CAAC,CAAA;QAEF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC7B,QAAQ,OAAO,CAAC,IAAI,IAAI,KAAK,EAAE,CAAC;gBAC5B,KAAK,KAAK,CAAC;gBACX,KAAK,cAAc,CAAC;gBACpB,KAAK,WAAW;oBACZ,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;oBAC5B,MAAK;gBAET,KAAK,MAAM,CAAC;gBACZ,KAAK,OAAO;oBACR,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;wBACjB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAA;oBACjD,CAAC;yBAAM,CAAC;wBACJ,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;oBACjC,CAAC;oBACD,MAAK;gBAET,KAAK,MAAM,CAAC;gBACZ,KAAK,YAAY;oBACb,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;wBACjB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAA;oBACjD,CAAC;yBAAM,CAAC;wBACJ,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;oBACjC,CAAC;oBACD,MAAK;gBAET,KAAK,OAAO,CAAC;gBACb,KAAK,aAAa;oBACd,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;wBACjB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAA;oBAClD,CAAC;yBAAM,CAAC;wBACJ,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;oBAClC,CAAC;oBACD,MAAK;YACb,CAAC;QACL,CAAC;IACL,CAAC;CACJ;AAlDD,kDAkDC", "file": "SimpleConsoleLogger.js", "sourcesContent": ["import { AbstractLogger } from \"./AbstractLogger\"\nimport { LogLevel, LogMessage } from \"./Logger\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\n\n/**\n * Performs logging of the events in TypeORM.\n * This version of logger uses console to log events and does not use syntax highlighting.\n */\nexport class SimpleConsoleLogger extends AbstractLogger {\n    /**\n     * Write log to specific output.\n     */\n    protected writeLog(\n        level: LogLevel,\n        logMessage: LogMessage | LogMessage[],\n        queryRunner?: QueryRunner,\n    ) {\n        const messages = this.prepareLogMessages(logMessage, {\n            highlightSql: false,\n        })\n\n        for (const message of messages) {\n            switch (message.type ?? level) {\n                case \"log\":\n                case \"schema-build\":\n                case \"migration\":\n                    console.log(message.message)\n                    break\n\n                case \"info\":\n                case \"query\":\n                    if (message.prefix) {\n                        console.info(message.prefix, message.message)\n                    } else {\n                        console.info(message.message)\n                    }\n                    break\n\n                case \"warn\":\n                case \"query-slow\":\n                    if (message.prefix) {\n                        console.warn(message.prefix, message.message)\n                    } else {\n                        console.warn(message.message)\n                    }\n                    break\n\n                case \"error\":\n                case \"query-error\":\n                    if (message.prefix) {\n                        console.error(message.prefix, message.message)\n                    } else {\n                        console.error(message.message)\n                    }\n                    break\n            }\n        }\n    }\n}\n"], "sourceRoot": ".."}