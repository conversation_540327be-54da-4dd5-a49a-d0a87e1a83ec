import jwt from 'jsonwebtoken';
import { AppDataSource } from '../config/db';
import { User } from '../models/user.model';

export interface SignupData {
  username: string;
  email: string;
  password: string;
  phone?: string;
  role?: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  user?: Partial<User>;
  token?: string;
}

export class AuthService {
  private userRepository = AppDataSource.getRepository(User);

  // Generate JWT token
  private generateToken(userId: string): string {
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      throw new Error('JWT_SECRET is not defined in environment variables');
    }

    return jwt.sign(
      { userId },
      secret,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );
  }

  // Verify JWT token
  public verifyToken(token: string): any {
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      throw new Error('JWT_SECRET is not defined in environment variables');
    }

    return jwt.verify(token, secret);
  }

  // User signup
  async signup(signupData: SignupData): Promise<AuthResponse> {
    try {
      const { username, email, password, phone, role } = signupData;

      // Check if user already exists
      const existingUser = await this.userRepository.findOne({
        where: [{ email }, { username }]
      });

      if (existingUser) {
        return {
          success: false,
          message: existingUser.email === email 
            ? 'User with this email already exists' 
            : 'Username already taken'
        };
      }

      // Create new user
      const user = new User();
      user.id = User.generateUserId();
      user.username = username;
      user.email = email;
      user.password = password; // Will be hashed by @BeforeInsert
      user.phone = phone || '';
      user.role = role || 'Student';

      // Save user (password will be automatically hashed)
      const savedUser = await this.userRepository.save(user);

      // Generate token
      const token = this.generateToken(savedUser.id);

      return {
        success: true,
        message: 'User registered successfully',
        user: savedUser.toJSON(),
        token
      };

    } catch (error) {
      console.error('Signup error:', error);
      return {
        success: false,
        message: 'Internal server error during signup'
      };
    }
  }

  // User login
  async login(loginData: LoginData): Promise<AuthResponse> {
    try {
      const { email, password } = loginData;

      // Find user by email
      const user = await this.userRepository.findOne({
        where: { email }
      });

      if (!user) {
        return {
          success: false,
          message: 'Invalid email or password'
        };
      }

      // Check password
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        return {
          success: false,
          message: 'Invalid email or password'
        };
      }

      // Generate token
      const token = this.generateToken(user.id);

      return {
        success: true,
        message: 'Login successful',
        user: user.toJSON(),
        token
      };

    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        message: 'Internal server error during login'
      };
    }
  }

  // Get user by ID
  async getUserById(userId: string): Promise<User | null> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId }
      });
      return user;
    } catch (error) {
      console.error('Get user error:', error);
      return null;
    }
  }

  // Update user profile
  async updateProfile(userId: string, updateData: Partial<User>): Promise<AuthResponse> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId }
      });

      if (!user) {
        return {
          success: false,
          message: 'User not found'
        };
      }

      // Update allowed fields
      const allowedFields = ['username', 'phone', 'role'];
      allowedFields.forEach(field => {
        if (updateData[field as keyof User] !== undefined) {
          (user as any)[field] = updateData[field as keyof User];
        }
      });

      const updatedUser = await this.userRepository.save(user);

      return {
        success: true,
        message: 'Profile updated successfully',
        user: updatedUser.toJSON()
      };

    } catch (error) {
      console.error('Update profile error:', error);
      return {
        success: false,
        message: 'Internal server error during profile update'
      };
    }
  }

  // Change password
  async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<AuthResponse> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId }
      });

      if (!user) {
        return {
          success: false,
          message: 'User not found'
        };
      }

      // Verify current password
      const isCurrentPasswordValid = await user.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        return {
          success: false,
          message: 'Current password is incorrect'
        };
      }

      // Update password (will be hashed by @BeforeUpdate)
      user.password = newPassword;
      await this.userRepository.save(user);

      return {
        success: true,
        message: 'Password changed successfully'
      };

    } catch (error) {
      console.error('Change password error:', error);
      return {
        success: false,
        message: 'Internal server error during password change'
      };
    }
  }
}
